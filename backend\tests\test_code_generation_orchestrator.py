"""
Tests for CodeGenerationOrchestrator - Complete workflow coordination and project assembly.

Tests the comprehensive orchestration of all components in the Code Generation & Assembly Module
to provide end-to-end code generation workflows with quality-first generation.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from backend.src.modules.code_generation.code_generation_orchestrator import (
    CodeGenerationOrchestrator, OrchestrationPhase, GenerationStrategy
)
from backend.src.modules.code_generation.component_api_analyzer import ComponentAPIAnalyzer
from backend.src.modules.code_generation.context_weaver import ContextWeaver
from backend.src.modules.code_generation.specialized_llm_client import SpecializedLLMClient
from backend.src.modules.code_generation.adapter_generator import AdapterGenerator
from backend.src.modules.code_generation.test_generation_engine import CodeTestGenerationEngine
from backend.src.modules.code_generation.validation_pipeline import ValidationPipeline
from backend.src.state.components import (
    AdapterCode, ValidationResult, ValidationStatus, ComponentTestResult,
    ComponentTestStatus, GenerationContext, CodeGenerationType, ProjectStructure,
    IntegrationComplexity, GitHubRepo
)
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestCodeGenerationOrchestrator:
    """Test CodeGenerationOrchestrator functionality"""
    
    @pytest.fixture
    def mock_components(self):
        """Create mock component dependencies"""
        return {
            "api_analyzer": Mock(spec=ComponentAPIAnalyzer),
            "context_weaver": Mock(spec=ContextWeaver),
            "llm_client": Mock(spec=SpecializedLLMClient),
            "adapter_generator": Mock(spec=AdapterGenerator),
            "test_engine": Mock(spec=CodeTestGenerationEngine),
            "validation_pipeline": Mock(spec=ValidationPipeline)
        }
    
    @pytest.fixture
    def orchestrator(self, mock_components):
        """Create CodeGenerationOrchestrator instance for testing"""
        return CodeGenerationOrchestrator(
            api_analyzer=mock_components["api_analyzer"],
            context_weaver=mock_components["context_weaver"],
            llm_client=mock_components["llm_client"],
            adapter_generator=mock_components["adapter_generator"],
            test_engine=mock_components["test_engine"],
            validation_pipeline=mock_components["validation_pipeline"]
        )
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state with selected components"""
        project_state = ProjectState(
            session_id="test-session",
            project_name="Authentication Service",
            project_brief="Microservice authentication system",
            target_patterns=["authentication", "rest_api"],
            status=ProjectStatus.QUILTING
        )
        
        # Add selected components
        project_state.selected_components = [
            GitHubRepo(
                name="auth-service",
                full_name="example/auth-service",
                description="Authentication microservice",
                language="Python",
                stars=150,
                url="https://github.com/example/auth-service"
            ),
            GitHubRepo(
                name="user-service",
                full_name="example/user-service",
                description="User management service",
                language="Python",
                stars=200,
                url="https://github.com/example/user-service"
            )
        ]
        
        return project_state
    
    @pytest.fixture
    def sample_adapter_code(self):
        """Create sample adapter code"""
        return AdapterCode(
            source_component="auth-service",
            target_component="user-service",
            pattern_type="authentication",
            integration_complexity=IntegrationComplexity.MEDIUM,
            adapter_code='''
class AuthUserAdapter:
    """Adapter for auth-service to user-service integration."""
    
    def __init__(self, auth_service, user_service):
        self.auth_service = auth_service
        self.user_service = user_service
    
    def authenticate_user(self, credentials):
        """Authenticate user and return user data."""
        if self.auth_service.validate(credentials):
            return self.user_service.get_user(credentials.user_id)
        return None
''',
            test_code='''
import pytest
from unittest.mock import Mock

class TestAuthUserAdapter:
    def test_authenticate_user_success(self):
        auth_service = Mock()
        user_service = Mock()
        adapter = AuthUserAdapter(auth_service, user_service)
        
        auth_service.validate.return_value = True
        user_service.get_user.return_value = {"id": 1, "name": "test"}
        
        result = adapter.authenticate_user(Mock(user_id=1))
        assert result == {"id": 1, "name": "test"}
''',
            file_path="auth_user_adapter.py",
            language="python",
            test_coverage=0.85
        )
    
    def test_generation_strategies_initialization(self, orchestrator):
        """Test generation strategies are properly initialized"""
        strategies = orchestrator.generation_strategies
        
        # Verify all expected strategies are present
        expected_strategies = ["quality_first", "speed_optimized", "comprehensive", "minimal_viable"]
        for strategy in expected_strategies:
            assert strategy in strategies
            
            # Verify strategy structure
            strategy_config = strategies[strategy]
            assert "description" in strategy_config
            assert "validation_gates" in strategy_config
            assert "test_coverage_target" in strategy_config
            assert "estimated_time_multiplier" in strategy_config
    
    def test_project_templates_initialization(self, orchestrator):
        """Test project templates are properly initialized"""
        templates = orchestrator.project_templates
        
        # Verify all expected languages are present
        expected_languages = ["python", "javascript", "java"]
        for language in expected_languages:
            assert language in templates
            
            # Verify template structure
            template = templates[language]
            assert "structure" in template
            assert "entry_point" in template
            assert isinstance(template["structure"], dict)
    
    def test_recovery_strategies_initialization(self, orchestrator):
        """Test error recovery strategies are properly initialized"""
        strategies = orchestrator.recovery_strategies
        
        # Verify all expected recovery scenarios are present
        expected_scenarios = [
            "component_analysis_failure",
            "adapter_generation_failure", 
            "test_generation_failure",
            "validation_failure"
        ]
        for scenario in expected_scenarios:
            assert scenario in strategies
            
            # Verify recovery strategy structure
            strategy = strategies[scenario]
            assert "retry_count" in strategy
            assert "fallback_action" in strategy
            assert "recovery_time" in strategy
    
    def test_quality_gates_configuration(self, orchestrator):
        """Test quality gates configuration"""
        gates = orchestrator.quality_gates
        
        # Verify essential gates are enabled
        assert gates["syntax_validation"] is True
        assert gates["compilation_check"] is True
        assert gates["test_execution"] is True
        assert gates["security_scan"] is True
        assert gates["production_readiness"] is True
        
        # Verify optional gates can be disabled
        assert isinstance(gates["performance_check"], bool)
        assert isinstance(gates["best_practices"], bool)
    
    @pytest.mark.asyncio
    async def test_orchestrate_code_generation_success(self, orchestrator, mock_components, 
                                                      sample_project_state, sample_adapter_code):
        """Test successful code generation orchestration"""
        # Setup mocks
        mock_components["api_analyzer"].analyze_component_api = AsyncMock(return_value={"mock": True})
        mock_components["context_weaver"].build_generation_context = AsyncMock(
            return_value=GenerationContext(goal="test", generation_type=CodeGenerationType.ADAPTER)
        )
        mock_components["adapter_generator"].generate_adapter = AsyncMock(return_value=sample_adapter_code)
        mock_components["test_engine"].generate_test_suite = AsyncMock(
            return_value=ComponentTestResult(
                component_name="test",
                test_type="unit",
                status=ComponentTestStatus.PASSED
            )
        )
        mock_components["validation_pipeline"].validate_code = AsyncMock(
            return_value=ValidationResult(
                code_id=sample_adapter_code.id,
                validation_type="comprehensive",
                status=ValidationStatus.PASSED,
                passed=True
            )
        )
        
        # Track progress calls
        progress_calls = []
        async def progress_callback(phase, message):
            progress_calls.append((phase, message))
        
        # Execute orchestration
        project_structure, suggested_actions = await orchestrator.orchestrate_code_generation(
            sample_project_state,
            GenerationStrategy.QUALITY_FIRST,
            progress_callback
        )
        
        # Verify results
        assert isinstance(project_structure, ProjectStructure)
        assert project_structure.project_name == sample_project_state.project_name
        assert isinstance(suggested_actions, list)
        assert len(suggested_actions) > 0
        
        # Verify progress tracking
        assert len(progress_calls) > 0
        phases_called = [call[0] for call in progress_calls]
        assert OrchestrationPhase.INITIALIZATION in phases_called
        assert OrchestrationPhase.FINALIZATION in phases_called
    
    @pytest.mark.asyncio
    async def test_orchestrate_code_generation_different_strategies(self, orchestrator, mock_components,
                                                                   sample_project_state, sample_adapter_code):
        """Test orchestration with different generation strategies"""
        # Setup basic mocks
        mock_components["api_analyzer"].analyze_component_api = AsyncMock(return_value={"mock": True})
        mock_components["context_weaver"].build_generation_context = AsyncMock(
            return_value=GenerationContext(goal="test", generation_type=CodeGenerationType.ADAPTER)
        )
        mock_components["adapter_generator"].generate_adapter = AsyncMock(return_value=sample_adapter_code)
        mock_components["test_engine"].generate_test_suite = AsyncMock(
            return_value=ComponentTestResult(component_name="test", test_type="unit")
        )
        mock_components["validation_pipeline"].validate_code = AsyncMock(
            return_value=ValidationResult(code_id=sample_adapter_code.id, validation_type="test")
        )
        
        # Test different strategies
        strategies = [
            GenerationStrategy.QUALITY_FIRST,
            GenerationStrategy.SPEED_OPTIMIZED,
            GenerationStrategy.COMPREHENSIVE,
            GenerationStrategy.MINIMAL_VIABLE
        ]
        
        for strategy in strategies:
            project_structure, actions = await orchestrator.orchestrate_code_generation(
                sample_project_state, strategy
            )
            
            assert isinstance(project_structure, ProjectStructure)
            assert isinstance(actions, list)
            
            # Verify strategy was tracked
            assert strategy.value in orchestrator.orchestration_stats["strategy_usage"]

    @pytest.mark.asyncio
    async def test_orchestrate_code_generation_with_failures(self, orchestrator, mock_components,
                                                            sample_project_state):
        """Test orchestration with component failures and recovery"""
        # Setup failing mocks
        mock_components["api_analyzer"].analyze_component_api = AsyncMock(side_effect=Exception("API analysis failed"))
        mock_components["context_weaver"].build_generation_context = AsyncMock(side_effect=Exception("Context failed"))

        # Execute orchestration (should use fallbacks)
        project_structure, suggested_actions = await orchestrator.orchestrate_code_generation(
            sample_project_state,
            GenerationStrategy.MINIMAL_VIABLE
        )

        # Should return fallback structure
        assert isinstance(project_structure, ProjectStructure)
        assert project_structure.project_name == sample_project_state.project_name
        assert len(suggested_actions) > 0
        assert any("error" in action.lower() for action in suggested_actions)

    @pytest.mark.asyncio
    async def test_analyze_components(self, orchestrator, mock_components, sample_project_state):
        """Test component analysis phase"""
        # Setup mock
        mock_components["api_analyzer"].analyze_component_api = AsyncMock(
            return_value={"methods": ["authenticate", "validate"], "classes": ["AuthService"]}
        )

        # Execute analysis
        component_apis = await orchestrator._analyze_components(sample_project_state)

        # Verify results
        assert isinstance(component_apis, dict)
        assert len(component_apis) == len(sample_project_state.selected_components)

        # Verify each component was analyzed
        for component in sample_project_state.selected_components:
            assert component.name in component_apis
            assert "methods" in component_apis[component.name]

    @pytest.mark.asyncio
    async def test_build_generation_context(self, orchestrator, mock_components, sample_project_state):
        """Test generation context building"""
        # Setup mock
        mock_context = GenerationContext(
            goal="Test generation",
            generation_type=CodeGenerationType.ADAPTER,
            project_language="python",
            project_framework="fastapi"
        )
        mock_components["context_weaver"].build_generation_context = AsyncMock(return_value=mock_context)

        # Execute context building
        component_apis = {"auth-service": {"mock": True}}
        strategy_config = {"description": "test strategy", "test_coverage_target": 0.8}

        context = await orchestrator._build_generation_context(
            sample_project_state, component_apis, strategy_config
        )

        # Verify results
        assert isinstance(context, GenerationContext)
        assert context.goal == "Test generation"
        assert context.project_language == "python"

    @pytest.mark.asyncio
    async def test_generate_adapters(self, orchestrator, mock_components, sample_adapter_code):
        """Test adapter generation phase"""
        # Setup mock
        mock_components["adapter_generator"].generate_adapter = AsyncMock(return_value=sample_adapter_code)

        # Execute adapter generation
        context = GenerationContext(goal="test", generation_type=CodeGenerationType.ADAPTER)
        strategy_config = {"test_coverage_target": 0.8}

        adapters = await orchestrator._generate_adapters(context, strategy_config)

        # Verify results
        assert isinstance(adapters, list)
        assert len(adapters) > 0
        assert isinstance(adapters[0], AdapterCode)
        assert adapters[0].source_component == sample_adapter_code.source_component

    @pytest.mark.asyncio
    async def test_generate_tests(self, orchestrator, mock_components, sample_adapter_code):
        """Test test generation phase"""
        # Setup mock
        mock_test_result = ComponentTestResult(
            component_name="test-component",
            test_type="unit",
            status=ComponentTestStatus.PASSED,
            passed=5,
            failed=0,
            coverage_percentage=0.85
        )
        mock_components["test_engine"].generate_test_suite = AsyncMock(return_value=mock_test_result)

        # Execute test generation
        adapters = [sample_adapter_code]
        context = GenerationContext(goal="test", generation_type=CodeGenerationType.ADAPTER)
        strategy_config = {"test_coverage_target": 0.8}

        test_results = await orchestrator._generate_tests(adapters, context, strategy_config)

        # Verify results
        assert isinstance(test_results, list)
        assert len(test_results) == len(adapters)
        assert isinstance(test_results[0], ComponentTestResult)
        assert test_results[0].status == ComponentTestStatus.PASSED

    @pytest.mark.asyncio
    async def test_validate_code(self, orchestrator, mock_components, sample_adapter_code, sample_project_state):
        """Test code validation phase"""
        # Setup mock
        mock_validation_result = ValidationResult(
            code_id=sample_adapter_code.id,
            validation_type="comprehensive",
            status=ValidationStatus.PASSED,
            passed=True,
            syntax_valid=True,
            compilation_successful=True,
            tests_passed=True
        )
        mock_components["validation_pipeline"].validate_code = AsyncMock(return_value=mock_validation_result)

        # Execute validation
        adapters = [sample_adapter_code]
        strategy_config = {"validation_gates": ["syntax", "compilation", "tests"]}

        validation_results = await orchestrator._validate_code(adapters, sample_project_state, strategy_config)

        # Verify results
        assert isinstance(validation_results, list)
        assert len(validation_results) == len(adapters)
        assert isinstance(validation_results[0], ValidationResult)
        assert validation_results[0].passed is True

    @pytest.mark.asyncio
    async def test_assemble_project(self, orchestrator, sample_project_state, sample_adapter_code):
        """Test project assembly phase"""
        # Create test data
        test_results = [ComponentTestResult(
            component_name="test",
            test_type="unit",
            status=ComponentTestStatus.PASSED
        )]
        validation_results = [ValidationResult(
            code_id=sample_adapter_code.id,
            validation_type="test",
            status=ValidationStatus.PASSED,
            passed=True
        )]
        strategy_config = {"documentation_generation": True}

        # Execute project assembly
        project_structure = await orchestrator._assemble_project(
            sample_project_state, [sample_adapter_code], test_results, validation_results, strategy_config
        )

        # Verify results
        assert isinstance(project_structure, ProjectStructure)
        assert project_structure.project_name == sample_project_state.project_name
        assert project_structure.language == "python"  # Default language
        assert project_structure.framework == "fastapi"  # Default framework
        assert len(project_structure.files) > 0
        assert len(project_structure.directories) > 0

        # Verify adapter files were added
        adapter_files = [path for path in project_structure.files.keys() if "adapter" in path]
        assert len(adapter_files) > 0

    def test_generate_suggested_actions(self, orchestrator, sample_adapter_code):
        """Test suggested actions generation"""
        # Create test data
        project_structure = ProjectStructure(
            project_name="Test Project",
            framework="fastapi",
            language="python"
        )
        project_structure.total_files = 5
        project_structure.readme_content = "Test README"

        validation_results = [ValidationResult(
            code_id=sample_adapter_code.id,
            validation_type="test",
            status=ValidationStatus.PASSED,
            passed=True,
            code_coverage=0.75
        )]
        strategy_config = {"test_coverage_target": 0.8, "performance_optimization": True}

        # Execute action generation
        actions = orchestrator._generate_suggested_actions(project_structure, validation_results, strategy_config)

        # Verify results
        assert isinstance(actions, list)
        assert len(actions) > 0

        # Should suggest improving test coverage (0.75 < 0.8)
        coverage_actions = [action for action in actions if "coverage" in action.lower()]
        assert len(coverage_actions) > 0

        # Should suggest performance optimization
        perf_actions = [action for action in actions if "performance" in action.lower()]
        assert len(perf_actions) > 0

    def test_determine_primary_language(self, orchestrator, sample_project_state):
        """Test primary language determination"""
        component_apis = {}

        language = orchestrator._determine_primary_language(sample_project_state, component_apis)

        # Should default to Python for now
        assert language == "python"

    def test_determine_primary_framework(self, orchestrator, sample_project_state):
        """Test primary framework determination"""
        component_apis = {}

        framework = orchestrator._determine_primary_framework(sample_project_state, component_apis)

        # Should default to FastAPI for now
        assert framework == "fastapi"

    def test_generate_file_content(self, orchestrator, sample_project_state):
        """Test file content generation"""
        # Test README template
        config = {"template": "python_readme"}
        strategy_config = {"description": "quality-first generation"}

        content = orchestrator._generate_file_content("README.md", config, sample_project_state, strategy_config)

        assert isinstance(content, str)
        assert sample_project_state.project_name in content
        assert sample_project_state.project_brief in content
        assert "CodeQuilter" in content

        # Test package.json template
        config = {"template": "package_json"}
        content = orchestrator._generate_file_content("package.json", config, sample_project_state, strategy_config)

        assert isinstance(content, str)
        assert sample_project_state.project_name.lower().replace(' ', '-') in content
        assert "jest" in content

    def test_add_configuration_files(self, orchestrator, sample_adapter_code):
        """Test configuration files addition"""
        project_structure = ProjectStructure(
            project_name="Test Project",
            framework="fastapi",
            language="python"
        )
        adapters = [sample_adapter_code]
        strategy_config = {"description": "Complete generation with all features enabled"}

        orchestrator._add_configuration_files(project_structure, adapters, strategy_config)

        # Verify environment config was added
        assert ".env.example" in project_structure.env_config
        env_content = project_structure.env_config[".env.example"]
        assert "Test Project" in env_content

        # Verify Docker config was added for comprehensive strategy
        assert "Dockerfile" in project_structure.docker_config
        dockerfile_content = project_structure.docker_config["Dockerfile"]
        assert "python:3.11-slim" in dockerfile_content

    def test_add_documentation_files(self, orchestrator, sample_adapter_code):
        """Test documentation files addition"""
        project_structure = ProjectStructure(
            project_name="Test Project",
            framework="fastapi",
            language="python"
        )
        adapters = [sample_adapter_code]
        validation_results = [ValidationResult(
            code_id=sample_adapter_code.id,
            validation_type="test",
            status=ValidationStatus.PASSED
        )]

        orchestrator._add_documentation_files(project_structure, adapters, validation_results)

        # Verify API documentation was added
        assert project_structure.api_documentation
        assert sample_adapter_code.source_component in project_structure.api_documentation
        assert sample_adapter_code.target_component in project_structure.api_documentation

        # Verify deployment guide was added
        assert project_structure.deployment_guide
        assert "Prerequisites" in project_structure.deployment_guide
        assert "Production Deployment" in project_structure.deployment_guide

    def test_create_fallback_adapter(self, orchestrator):
        """Test fallback adapter creation"""
        context = GenerationContext(
            goal="test",
            generation_type=CodeGenerationType.ADAPTER,
            selected_pattern="authentication",
            project_language="python"
        )

        adapter = orchestrator._create_fallback_adapter(context)

        assert isinstance(adapter, AdapterCode)
        assert adapter.source_component == "fallback-source"
        assert adapter.target_component == "fallback-target"
        assert adapter.pattern_type == context.selected_pattern
        assert adapter.language == context.project_language
        assert "FallbackAdapter" in adapter.adapter_code
        assert "TestFallbackAdapter" in adapter.test_code

    def test_create_fallback_test_result(self, orchestrator, sample_adapter_code):
        """Test fallback test result creation"""
        test_result = orchestrator._create_fallback_test_result(sample_adapter_code)

        assert isinstance(test_result, ComponentTestResult)
        assert test_result.status == ComponentTestStatus.PASSED
        assert test_result.passed == 1
        assert test_result.failed == 0
        assert test_result.coverage_percentage == 0.5

    def test_create_fallback_validation_result(self, orchestrator, sample_adapter_code):
        """Test fallback validation result creation"""
        validation_result = orchestrator._create_fallback_validation_result(sample_adapter_code)

        assert isinstance(validation_result, ValidationResult)
        assert validation_result.code_id == sample_adapter_code.id
        assert validation_result.status == ValidationStatus.PASSED
        assert validation_result.passed is True
        assert validation_result.syntax_valid is True
        assert validation_result.compilation_successful is True
        assert validation_result.tests_passed is True

    @pytest.mark.asyncio
    async def test_create_fallback_project_structure(self, orchestrator, sample_project_state):
        """Test fallback project structure creation"""
        project_structure = await orchestrator._create_fallback_project_structure(sample_project_state)

        assert isinstance(project_structure, ProjectStructure)
        assert project_structure.project_name == sample_project_state.project_name
        assert project_structure.framework == "fastapi"
        assert project_structure.language == "python"

        # Verify minimal files were created
        assert "src/__init__.py" in project_structure.files
        assert "src/main.py" in project_structure.files
        assert "requirements.txt" in project_structure.files
        assert "README.md" in project_structure.files

        # Verify directories were created
        assert "src/" in project_structure.directories
        assert "tests/" in project_structure.directories

    def test_track_orchestration_stats(self, orchestrator, sample_adapter_code):
        """Test orchestration statistics tracking"""
        initial_total = orchestrator.orchestration_stats["total_orchestrations"]

        strategy = GenerationStrategy.QUALITY_FIRST
        success = True
        orchestration_time = 10.5
        validation_results = [ValidationResult(
            code_id=sample_adapter_code.id,
            validation_type="test",
            status=ValidationStatus.PASSED,
            passed=True
        )]

        orchestrator._track_orchestration_stats(strategy, success, orchestration_time, validation_results)

        stats = orchestrator.orchestration_stats
        assert stats["total_orchestrations"] == initial_total + 1
        assert stats["successful_orchestrations"] == initial_total + 1
        assert strategy.value in stats["strategy_usage"]
        assert stats["strategy_usage"][strategy.value] >= 1
        assert len(stats["quality_scores"]) > 0
        assert stats["average_orchestration_time"] > 0

    def test_track_orchestration_stats_with_failure(self, orchestrator):
        """Test orchestration statistics tracking with failure"""
        initial_total = orchestrator.orchestration_stats["total_orchestrations"]
        initial_successful = orchestrator.orchestration_stats["successful_orchestrations"]

        strategy = GenerationStrategy.SPEED_OPTIMIZED
        success = False
        orchestration_time = 5.0
        validation_results = []

        orchestrator._track_orchestration_stats(strategy, success, orchestration_time, validation_results)

        stats = orchestrator.orchestration_stats
        assert stats["total_orchestrations"] == initial_total + 1
        assert stats["successful_orchestrations"] == initial_successful  # No change
        assert strategy.value in stats["strategy_usage"]

    def test_get_orchestration_statistics(self, orchestrator):
        """Test orchestration statistics retrieval"""
        # Add some mock stats
        orchestrator.orchestration_stats["total_orchestrations"] = 10
        orchestrator.orchestration_stats["successful_orchestrations"] = 8
        orchestrator.orchestration_stats["quality_scores"] = [0.8, 0.9, 0.7, 0.85]

        stats = orchestrator.get_orchestration_statistics()

        assert stats["success_rate"] == 0.8
        assert abs(stats["average_quality_score"] - 0.8125) < 0.001  # Allow for floating point precision
        assert "available_strategies" in stats
        assert "orchestration_phases" in stats
        assert len(stats["available_strategies"]) == 4  # All GenerationStrategy values
        assert len(stats["orchestration_phases"]) == 8   # All OrchestrationPhase values

    def test_create_fallback_component_apis(self, orchestrator, sample_project_state):
        """Test fallback component APIs creation"""
        component_apis = orchestrator._create_fallback_component_apis(sample_project_state.selected_components)

        assert isinstance(component_apis, dict)
        assert len(component_apis) == len(sample_project_state.selected_components)

        for component in sample_project_state.selected_components:
            assert component.name in component_apis
            assert component_apis[component.name]["mock"] is True
            assert "api_methods" in component_apis[component.name]
