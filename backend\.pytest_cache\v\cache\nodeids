["tests/test_adapter_generator.py::TestAdapterGenerator::test_adapter_patterns_initialization", "tests/test_adapter_generator.py::TestAdapterGenerator::test_calculate_pattern_suitability", "tests/test_adapter_generator.py::TestAdapterGenerator::test_calculate_quality_score", "tests/test_adapter_generator.py::TestAdapterGenerator::test_create_fallback_adapter", "tests/test_adapter_generator.py::TestAdapterGenerator::test_determine_adapter_pattern_authentication", "tests/test_adapter_generator.py::TestAdapterGenerator::test_determine_adapter_pattern_database", "tests/test_adapter_generator.py::TestAdapterGenerator::test_determine_adapter_pattern_fallback", "tests/test_adapter_generator.py::TestAdapterGenerator::test_determine_adapter_pattern_message_queue", "tests/test_adapter_generator.py::TestAdapterGenerator::test_determine_adapter_pattern_rest_api", "tests/test_adapter_generator.py::TestAdapterGenerator::test_enhance_context_for_adapter", "tests/test_adapter_generator.py::TestAdapterGenerator::test_enhance_context_high_complexity", "tests/test_adapter_generator.py::TestAdapterGenerator::test_extract_dependencies_javascript", "tests/test_adapter_generator.py::TestAdapterGenerator::test_extract_dependencies_python", "tests/test_adapter_generator.py::TestAdapterGenerator::test_generate_adapter_filename", "tests/test_adapter_generator.py::TestAdapterGenerator::test_generate_adapter_success", "tests/test_adapter_generator.py::TestAdapterGenerator::test_generate_adapter_with_specific_pattern", "tests/test_adapter_generator.py::TestAdapterGenerator::test_generate_multiple_adapters", "tests/test_adapter_generator.py::TestAdapterGenerator::test_get_generation_statistics", "tests/test_adapter_generator.py::TestAdapterGenerator::test_get_pattern_recommendations", "tests/test_adapter_generator.py::TestAdapterGenerator::test_track_generation_stats", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_complete_brainstorming", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_delete_brainstorming_session", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_delete_nonexistent_session", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_get_available_questions", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_get_brainstorming_status", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_get_status_nonexistent_session", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_start_brainstorming_duplicate_session", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_start_brainstorming_empty_description", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_start_brainstorming_success", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_submit_answer_invalid_question", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_submit_answer_nonexistent_session", "tests/test_api_brainstorming.py::TestBrainstormingAPI::test_submit_answer_success", "tests/test_api_brainstorming.py::TestBrainstormingAPIIntegration::test_complete_brainstorming_workflow", "tests/test_api_components.py::TestComponentSearchAPI::test_deselect_component_not_selected", "tests/test_api_components.py::TestComponentSearchAPI::test_deselect_component_success", "tests/test_api_components.py::TestComponentSearchAPI::test_get_component_candidates_empty", "tests/test_api_components.py::TestComponentSearchAPI::test_get_component_candidates_success", "tests/test_api_components.py::TestComponentSearchAPI::test_get_selected_components_empty", "tests/test_api_components.py::TestComponentSearchAPI::test_get_selected_components_success", "tests/test_api_components.py::TestComponentSearchAPI::test_project_status_transitions", "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_brainstorming_incomplete", "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_brainstorming_not_found", "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_project_not_found", "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_success", "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_custom_url_not_implemented", "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_not_found", "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_success", "tests/test_api_projects.py::TestProjectCRUD::test_create_project_basic", "tests/test_api_projects.py::TestProjectCRUD::test_create_project_invalid_pattern", "tests/test_api_projects.py::TestProjectCRUD::test_create_project_with_patterns", "tests/test_api_projects.py::TestProjectCRUD::test_delete_project", "tests/test_api_projects.py::TestProjectCRUD::test_get_nonexistent_project", "tests/test_api_projects.py::TestProjectCRUD::test_get_project", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_empty", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_pagination", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_with_data", "tests/test_api_projects.py::TestProjectCRUD::test_update_project", "tests/test_api_projects.py::TestProjectCRUD::test_update_project_status", "tests/test_api_projects.py::TestProjectPatterns::test_get_available_patterns", "tests/test_api_projects.py::TestProjectUtilities::test_clear_project_errors", "tests/test_api_projects.py::TestProjectUtilities::test_get_project_history", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_code_generation_no_components", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_code_generation_task_success", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_component_search_task_success", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_error_handling", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_execute_task_plan_not_approved", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_execute_task_plan_success", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_generate_task_plan_project_not_found", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_generate_task_plan_success", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_get_task_execution_status", "tests/test_api_task_execution.py::TestTaskExecutionAPI::test_get_task_execution_status_no_active_tasks", "tests/test_brainstorming.py::TestBrainstormingEngine::test_conversation_completion", "tests/test_brainstorming.py::TestBrainstormingEngine::test_get_session_status", "tests/test_brainstorming.py::TestBrainstormingEngine::test_process_answer", "tests/test_brainstorming.py::TestBrainstormingEngine::test_process_invalid_question", "tests/test_brainstorming.py::TestBrainstormingEngine::test_start_brainstorming", "tests/test_brainstorming.py::TestConversationManager::test_build_conversation_context", "tests/test_brainstorming.py::TestConversationManager::test_generate_confident_assumptions", "tests/test_brainstorming.py::TestConversationManager::test_get_intelligent_follow_up", "tests/test_brainstorming.py::TestDataModels::test_brainstorming_session_creation", "tests/test_brainstorming.py::TestDataModels::test_intelligent_decision_creation", "tests/test_brainstorming.py::TestDataModels::test_pattern_confidence_creation", "tests/test_brainstorming.py::TestDataModels::test_questionnaire_response_creation", "tests/test_brainstorming.py::TestDecisionEngine::test_make_authentication_decision", "tests/test_brainstorming.py::TestDecisionEngine::test_make_database_decision", "tests/test_brainstorming.py::TestDecisionEngine::test_make_web_framework_decision", "tests/test_brainstorming.py::TestDecisionEngine::test_unknown_decision_type", "tests/test_brainstorming.py::TestIntegration::test_complete_brainstorming_flow", "tests/test_brainstorming.py::TestPatternConfidenceCalculator::test_calculate_pattern_scores_api_project", "tests/test_brainstorming.py::TestPatternConfidenceCalculator::test_calculate_pattern_scores_empty", "tests/test_brainstorming.py::TestPatternConfidenceCalculator::test_get_recommended_patterns", "tests/test_brainstorming.py::TestProgressiveQuestionEngine::test_conditional_questions", "tests/test_brainstorming.py::TestProgressiveQuestionEngine::test_get_next_questions_initial", "tests/test_brainstorming.py::TestProgressiveQuestionEngine::test_get_next_questions_with_responses", "tests/test_brainstorming.py::TestQuestionDatabase::test_get_question_by_id", "tests/test_brainstorming.py::TestQuestionDatabase::test_get_questions_by_category", "tests/test_brainstorming.py::TestQuestionDatabase::test_question_database_completeness", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_add_configuration_files", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_add_documentation_files", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_analyze_components", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_assemble_project", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_build_generation_context", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_create_fallback_adapter", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_create_fallback_component_apis", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_create_fallback_project_structure", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_create_fallback_test_result", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_create_fallback_validation_result", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_determine_primary_framework", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_determine_primary_language", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_generate_adapters", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_generate_file_content", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_generate_suggested_actions", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_generate_tests", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_generation_strategies_initialization", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_get_orchestration_statistics", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_orchestrate_code_generation_different_strategies", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_orchestrate_code_generation_success", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_orchestrate_code_generation_with_failures", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_project_templates_initialization", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_quality_gates_configuration", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_recovery_strategies_initialization", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_track_orchestration_stats", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_track_orchestration_stats_with_failure", "tests/test_code_generation_orchestrator.py::TestCodeGenerationOrchestrator::test_validate_code", "tests/test_component_api_analyzer.py::TestAPIExtractionUtils::test_javascript_export_extraction", "tests/test_component_api_analyzer.py::TestAPIExtractionUtils::test_python_class_extraction", "tests/test_component_api_analyzer.py::TestAPIExtractionUtils::test_python_function_extraction", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_analyze_javascript_component_api", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_analyze_python_component_api", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_analyze_unsupported_language", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_complexity_score_calculation", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_component_api_serialization", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_error_handling_during_analysis", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_mock_javascript_file_generation", "tests/test_component_api_analyzer.py::TestComponentAPIAnalyzer::test_mock_python_file_generation", "tests/test_component_discovery.py::TestCandidateRepo::test_candidate_repo_creation", "tests/test_component_discovery.py::TestCandidateRepo::test_candidate_repo_from_github_data", "tests/test_component_discovery.py::TestCandidateRepo::test_candidate_repo_serialization", "tests/test_component_discovery.py::TestComponentMatcher::test_extract_requirements", "tests/test_component_discovery.py::TestComponentMatcher::test_find_best_components_empty_results", "tests/test_component_discovery.py::TestComponentMatcher::test_find_best_components_success", "tests/test_component_discovery.py::TestComponentMatcher::test_search_candidates", "tests/test_component_discovery.py::TestHealthReport::test_health_report_creation", "tests/test_component_discovery.py::TestHealthReport::test_health_report_serialization", "tests/test_component_discovery.py::TestRecommendedComponent::test_recommended_component_creation", "tests/test_component_discovery.py::TestRecommendedComponent::test_recommended_component_serialization", "tests/test_context_weaver.py::TestContextWeaver::test_assess_complexity", "tests/test_context_weaver.py::TestContextWeaver::test_build_component_api_from_dict", "tests/test_context_weaver.py::TestContextWeaver::test_build_generation_context_adapter", "tests/test_context_weaver.py::TestContextWeaver::test_build_generation_context_tests", "tests/test_context_weaver.py::TestContextWeaver::test_build_llm_prompt_comprehensive", "tests/test_context_weaver.py::TestContextWeaver::test_build_llm_prompt_minimal_context", "tests/test_context_weaver.py::TestContextWeaver::test_detect_project_framework", "tests/test_context_weaver.py::TestContextWeaver::test_detect_project_language", "tests/test_context_weaver.py::TestContextWeaver::test_determine_pattern", "tests/test_context_weaver.py::TestContextWeaver::test_error_handling_in_context_building", "tests/test_context_weaver.py::TestContextWeaver::test_get_pattern_templates", "tests/test_context_weaver.py::TestContextWeaver::test_get_similar_examples", "tests/test_context_weaver.py::TestContextWeaver::test_knowledge_base_initialization", "tests/test_context_weaver.py::TestContextWeaver::test_prompt_optimization", "tests/test_context_weaver.py::TestContextWeaver::test_select_optimal_model", "tests/test_github_search.py::TestGitHubClientFactory::test_create_mock_client_default", "tests/test_github_search.py::TestGitHubClientFactory::test_create_mock_client_explicit", "tests/test_github_search.py::TestGitHubClientFactory::test_create_real_client_no_token", "tests/test_github_search.py::TestGitHubClientFactory::test_create_real_client_with_token", "tests/test_github_search.py::TestGitHubSearchIntegration::test_end_to_end_search_flow", "tests/test_github_search.py::TestGitHubSearchIntegration::test_multiple_pattern_search", "tests/test_github_search.py::TestMockGitHubClient::test_connection_test", "tests/test_github_search.py::TestMockGitHubClient::test_get_license_info", "tests/test_github_search.py::TestMockGitHubClient::test_get_license_info_not_found", "tests/test_github_search.py::TestMockGitHubClient::test_get_repository_details", "tests/test_github_search.py::TestMockGitHubClient::test_get_repository_details_not_found", "tests/test_github_search.py::TestMockGitHubClient::test_mock_repository_data_quality", "tests/test_github_search.py::TestMockGitHubClient::test_pattern_keyword_mapping", "tests/test_github_search.py::TestMockGitHubClient::test_search_repositories_codequilter_decide", "tests/test_github_search.py::TestMockGitHubClient::test_search_repositories_language_filter", "tests/test_github_search.py::TestMockGitHubClient::test_search_repositories_message_queue", "tests/test_github_search.py::TestMockGitHubClient::test_search_repositories_rest_api", "tests/test_github_search.py::TestRealGitHubClient::test_real_client_not_implemented", "tests/test_github_search.py::TestRealGitHubClient::test_search_not_implemented", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_activity_score_calculation", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_analyze_dormant_repo", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_analyze_high_quality_repo", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_analyze_no_license_repo", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_custom_license_compatibility", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_license_score_calculation", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_maintenance_status_determination", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_popularity_score_calculation", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_positive_indicator_identification", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_risk_factor_identification", "tests/test_health_analysis.py::TestComponentHealthAnalyzer::test_security_score_calculation", "tests/test_main.py::test_health_check", "tests/test_main.py::test_mock_projects_endpoint", "tests/test_main.py::test_projects_endpoint", "tests/test_main.py::test_root_endpoint", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_build_correction_prompt", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_correct_code_success", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_correction_tracking", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_cost_calculation", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_fallback_generation", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_generate_code_success", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_generate_code_with_preferred_model", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_generate_mock_code_fast", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_generate_mock_code_generic", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_generate_mock_code_quality", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_get_model_recommendations", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_get_required_capabilities", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_get_required_capabilities_complex", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_get_usage_statistics", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_model_config_initialization", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_model_tier_classification", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_optimal_model_selection_adapter", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_optimal_model_selection_quality_levels", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_optimal_model_selection_tests", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_preferred_model_override", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_select_from_candidates", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_select_from_candidates_empty", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_task_complexity_assessment_complex", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_task_complexity_assessment_simple", "tests/test_specialized_llm_client.py::TestSpecializedLLMClient::test_usage_tracking", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_categories", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_palette_completeness", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_search_query_generation", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_suggestion", "tests/test_state_models.py::TestComponentTestResult::test_failing_tests", "tests/test_state_models.py::TestComponentTestResult::test_test_result_creation", "tests/test_state_models.py::TestGeneratedCode::test_code_creation", "tests/test_state_models.py::TestGeneratedCode::test_metrics_calculation", "tests/test_state_models.py::TestGitHubRepo::test_activity_check", "tests/test_state_models.py::TestGitHubRepo::test_quality_score_calculation", "tests/test_state_models.py::TestGitHubRepo::test_repo_creation", "tests/test_state_models.py::TestGitHubRepo::test_repo_serialization", "tests/test_state_models.py::TestProjectState::test_component_selection", "tests/test_state_models.py::TestProjectState::test_error_handling", "tests/test_state_models.py::TestProjectState::test_progress_calculation", "tests/test_state_models.py::TestProjectState::test_project_state_creation", "tests/test_state_models.py::TestProjectState::test_serialization", "tests/test_state_models.py::TestProjectState::test_status_update", "tests/test_state_models.py::TestSecurityReport::test_risk_calculation", "tests/test_state_models.py::TestSecurityReport::test_safety_assessment", "tests/test_state_models.py::TestSecurityReport::test_security_report_creation", "tests/test_state_models.py::TestTestResult::test_failing_tests", "tests/test_state_models.py::TestTestResult::test_test_result_creation", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_activity_callback_error_handling", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_activity_callback_integration", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_execute_successful_tasks", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_execute_with_failure", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_execution_result_summary_messages", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_plan_authentication_goal", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_plan_code_generation_goal", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_plan_component_search_goal", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_plan_generic_goal", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_project_state_integration", "tests/test_task_execution_agent.py::TestTaskExecutionAgent::test_task_type_determination", "tests/test_task_execution_failure_scenarios.py::TestErrorRecoveryScenarios::test_partial_success_recovery", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_activity_callback_failure_resilience", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_first_task_failure_stops_execution", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_memory_pressure_simulation", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_middle_task_failure_stops_execution", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_network_failure_scenario", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_plan_generation_failure", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_project_state_corruption_handling", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_resource_not_found_failure", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_timeout_failure_scenario", "tests/test_task_execution_failure_scenarios.py::TestTaskExecutionFailureScenarios::test_unexpected_exception_handling", "tests/test_task_execution_failure_scenarios.py::TestWebSocketFailureScenarios::test_plan_generation_websocket_failure", "tests/test_task_execution_failure_scenarios.py::TestWebSocketFailureScenarios::test_websocket_connection_failure", "tests/test_task_execution_failure_scenarios.py::TestWebSocketFailureScenarios::test_websocket_send_failure", "tests/test_task_models.py::TestExecutionResult::test_execution_result_serialization", "tests/test_task_models.py::TestExecutionResult::test_failed_execution_result", "tests/test_task_models.py::TestExecutionResult::test_failure_suggested_actions_no_retry", "tests/test_task_models.py::TestExecutionResult::test_failure_suggested_actions_with_retry", "tests/test_task_models.py::TestExecutionResult::test_success_suggested_actions", "tests/test_task_models.py::TestExecutionResult::test_successful_execution_result", "tests/test_task_models.py::TestSuggestedAction::test_suggested_action_creation", "tests/test_task_models.py::TestSuggestedAction::test_suggested_action_with_metadata", "tests/test_task_models.py::TestTask::test_task_activity_updates", "tests/test_task_models.py::TestTask::test_task_completion", "tests/test_task_models.py::TestTask::test_task_creation", "tests/test_task_models.py::TestTask::test_task_duration_calculation", "tests/test_task_models.py::TestTask::test_task_failure", "tests/test_task_models.py::TestTask::test_task_lifecycle_start", "tests/test_task_models.py::TestTask::test_task_serialization", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_build_test_context", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_build_test_prompt_integration_tests", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_build_test_prompt_unit_tests", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_calculate_test_coverage_basic", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_calculate_test_coverage_no_tests", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_combine_test_files", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_coverage_targets_initialization", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_create_failed_test_result", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_deduplicate_imports", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_detect_testing_framework_java", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_detect_testing_framework_javascript", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_detect_testing_framework_python", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_detect_testing_framework_unknown", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_enhance_context_for_test_type", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_fix_test_naming_pytest", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_framework_configs_initialization", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_fallback_tests", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_filename_jest", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_filename_junit", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_filename_pytest", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_suite_default_params", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_suite_llm_failure", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_generate_test_suite_success", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_get_framework_recommendations_java", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_get_framework_recommendations_javascript", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_get_framework_recommendations_python", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_get_generation_statistics", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_test_patterns_initialization", "tests/test_test_generation_engine.py::TestCodeTestGenerationEngine::test_track_test_generation_stats", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_build_test_context", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_build_test_prompt_integration_tests", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_build_test_prompt_unit_tests", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_calculate_test_coverage_basic", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_calculate_test_coverage_no_tests", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_combine_test_files", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_coverage_targets_initialization", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_create_failed_test_result", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_deduplicate_imports", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_detect_testing_framework_java", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_detect_testing_framework_javascript", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_detect_testing_framework_python", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_detect_testing_framework_unknown", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_enhance_context_for_test_type", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_fix_test_naming_pytest", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_framework_configs_initialization", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_fallback_tests", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_filename_jest", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_filename_junit", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_filename_pytest", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_suite_default_params", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_suite_llm_failure", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_generate_test_suite_success", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_get_framework_recommendations_java", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_get_framework_recommendations_javascript", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_get_framework_recommendations_python", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_get_generation_statistics", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_test_patterns_initialization", "tests/test_test_generation_engine.py::TestTestGenerationEngine::test_track_test_generation_stats", "tests/test_validation_pipeline.py::TestValidationPipeline::test_calculate_quality_score", "tests/test_validation_pipeline.py::TestValidationPipeline::test_calculate_quality_score_with_failures", "tests/test_validation_pipeline.py::TestValidationPipeline::test_create_failed_validation_result", "tests/test_validation_pipeline.py::TestValidationPipeline::test_create_sandbox_environment", "tests/test_validation_pipeline.py::TestValidationPipeline::test_generate_recommendations", "tests/test_validation_pipeline.py::TestValidationPipeline::test_get_file_extension", "tests/test_validation_pipeline.py::TestValidationPipeline::test_get_validation_recommendations_javascript", "tests/test_validation_pipeline.py::TestValidationPipeline::test_get_validation_recommendations_python", "tests/test_validation_pipeline.py::TestValidationPipeline::test_get_validation_recommendations_with_context", "tests/test_validation_pipeline.py::TestValidationPipeline::test_get_validation_statistics", "tests/test_validation_pipeline.py::TestValidationPipeline::test_language_validators_initialization", "tests/test_validation_pipeline.py::TestValidationPipeline::test_quality_thresholds_initialization", "tests/test_validation_pipeline.py::TestValidationPipeline::test_sandbox_config_initialization", "tests/test_validation_pipeline.py::TestValidationPipeline::test_security_scanners_initialization", "tests/test_validation_pipeline.py::TestValidationPipeline::test_track_validation_stats", "tests/test_validation_pipeline.py::TestValidationPipeline::test_track_validation_stats_with_failures", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_best_practices_good_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_best_practices_poor_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_code_success", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_code_with_all_gates", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_compilation_python_success", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_performance_good_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_performance_poor_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_production_readiness_good_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_security_clean_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_security_dangerous_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_syntax_python_failure", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_syntax_python_success", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_tests_no_test_code", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validate_tests_success", "tests/test_validation_pipeline.py::TestValidationPipeline::test_validation_gates_initialization", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_activity_callback", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_error_handling", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_message_batching", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_ping_pong", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_plan_approval_success", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_plan_rejection", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_plan_request_handling", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_websocket_connection", "tests/test_websocket.py::TestTaskExecutionWebSocketManager::test_websocket_disconnection", "tests/test_websocket.py::TestWebSocketIntegration::test_websocket_endpoint_exists", "tests/test_websocket.py::TestWebSocketIntegration::test_websocket_message_types"]