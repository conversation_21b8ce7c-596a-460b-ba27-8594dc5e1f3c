"""
Data models for components, repositories, and generated code.

This module defines the structures for representing GitHub repositories,
generated adapter code, test results, and other component-related data.

Enhanced for Code Generation & Assembly Module with component API analysis,
adapter generation, validation pipeline, and quality-first generation workflow.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import uuid


class ComponentStatus(Enum):
    """Status of a component in the project"""
    DISCOVERED = "discovered"
    ANALYZING = "analyzing"
    SELECTED = "selected"
    INTEGRATING = "integrating"
    INTEGRATED = "integrated"
    FAILED = "failed"


class ComponentTestStatus(Enum):
    """Status of component testing"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class SecurityLevel(Enum):
    """Security assessment levels"""
    SAFE = "safe"
    LOW_RISK = "low_risk"
    MEDIUM_RISK = "medium_risk"
    HIGH_RISK = "high_risk"
    CRITICAL = "critical"


class CodeGenerationType(Enum):
    """Types of code generation tasks"""
    ADAPTER = "adapter"
    CONFIGURATION = "configuration"
    TESTS = "tests"
    DOCUMENTATION = "documentation"
    PROJECT_STRUCTURE = "project_structure"


class ValidationStatus(Enum):
    """Status of code validation"""
    PENDING = "pending"
    VALIDATING = "validating"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class IntegrationComplexity(Enum):
    """Complexity levels for component integration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class GitHubRepo:
    """
    Represents a GitHub repository that could be used as a component.
    
    Contains all metadata needed for evaluation, selection, and integration.
    """
    
    # Basic Repository Info
    name: str
    full_name: str  # owner/repo
    description: str = ""
    url: str = ""
    clone_url: str = ""
    
    # Quality Metrics
    stars: int = 0
    forks: int = 0
    watchers: int = 0
    open_issues: int = 0
    closed_issues: int = 0
    
    # Activity Metrics
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pushed_at: Optional[datetime] = None
    
    # Technical Details
    language: str = ""
    languages: Dict[str, int] = field(default_factory=dict)  # language -> bytes
    license: Optional[str] = None
    topics: List[str] = field(default_factory=list)
    
    # CodeQuilter Analysis
    pattern_match: str = ""  # Which pattern this repo implements
    confidence_score: float = 0.0  # 0-1 confidence in pattern match
    compatibility_score: float = 0.0  # 0-1 compatibility with other components
    status: ComponentStatus = ComponentStatus.DISCOVERED
    
    # Integration Details
    main_files: List[str] = field(default_factory=list)  # Key files identified
    api_surface: Dict[str, Any] = field(default_factory=dict)  # Extracted API info
    dependencies: List[str] = field(default_factory=list)  # Package dependencies
    
    def get_quality_score(self) -> float:
        """Calculate overall quality score (0-1)"""
        # Weighted scoring based on multiple factors
        star_score = min(self.stars / 10000, 1.0) * 0.3  # Max 10k stars
        activity_score = 0.2 if self.pushed_at and (datetime.now() - self.pushed_at).days < 365 else 0.0
        issue_score = 0.2 if self.open_issues < self.closed_issues else 0.1
        license_score = 0.3 if self.license in ["MIT", "Apache-2.0", "BSD-3-Clause"] else 0.0
        
        return star_score + activity_score + issue_score + license_score
    
    def is_recently_active(self, days: int = 365) -> bool:
        """Check if repo has been active recently"""
        if not self.pushed_at:
            return False
        return (datetime.now() - self.pushed_at).days <= days
    
    def get_primary_language(self) -> str:
        """Get the primary programming language"""
        if not self.languages:
            return self.language
        return max(self.languages.keys(), key=lambda k: self.languages[k])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "name": self.name,
            "full_name": self.full_name,
            "description": self.description,
            "url": self.url,
            "stars": self.stars,
            "language": self.get_primary_language(),
            "license": self.license,
            "pattern_match": self.pattern_match,
            "confidence_score": self.confidence_score,
            "quality_score": self.get_quality_score(),
            "recently_active": self.is_recently_active(),
            "status": self.status.value
        }


@dataclass
class GeneratedCode:
    """
    Represents code generated by CodeQuilter for component integration.
    """
    
    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    
    # Generation Context
    source_component: str = ""  # Source component name
    target_component: str = ""  # Target component name
    pattern_type: str = ""      # Pattern being implemented
    
    # Generated Content
    code: str = ""              # The actual generated code
    file_path: str = ""         # Where this code should be placed
    language: str = "python"    # Programming language
    
    # Metadata
    description: str = ""       # Human-readable description
    dependencies: List[str] = field(default_factory=list)  # Required imports/packages
    test_code: str = ""         # Generated test code
    
    # Quality Metrics
    lines_of_code: int = 0
    complexity_score: float = 0.0
    test_coverage: float = 0.0
    
    def calculate_metrics(self) -> None:
        """Calculate code quality metrics"""
        self.lines_of_code = len([line for line in self.code.split('\n') if line.strip()])
        # TODO: REPLACE_MOCK - Implement real complexity analysis
        self.complexity_score = min(self.lines_of_code / 100, 1.0)  # Simple mock
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "source_component": self.source_component,
            "target_component": self.target_component,
            "pattern_type": self.pattern_type,
            "file_path": self.file_path,
            "language": self.language,
            "description": self.description,
            "lines_of_code": self.lines_of_code,
            "dependencies": self.dependencies
        }


@dataclass
class ComponentTestResult:
    """
    Results from testing a component or integration.
    """

    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)

    # Test Context
    component_name: str = ""
    test_type: str = ""  # "unit", "integration", "security", "performance"

    # Results
    status: ComponentTestStatus = ComponentTestStatus.PENDING
    passed: int = 0
    failed: int = 0
    skipped: int = 0
    duration_seconds: float = 0.0
    
    # Details
    output: str = ""            # Test output/logs
    error_message: str = ""     # Error details if failed
    coverage_percentage: float = 0.0
    
    # Files
    test_files: List[str] = field(default_factory=list)
    
    def get_success_rate(self) -> float:
        """Calculate test success rate"""
        total = self.passed + self.failed
        return (self.passed / total) if total > 0 else 0.0
    
    def is_passing(self) -> bool:
        """Check if tests are passing"""
        return self.status == ComponentTestStatus.PASSED and self.failed == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "component_name": self.component_name,
            "test_type": self.test_type,
            "status": self.status.value,
            "passed": self.passed,
            "failed": self.failed,
            "success_rate": self.get_success_rate(),
            "duration_seconds": self.duration_seconds,
            "coverage_percentage": self.coverage_percentage,
            "is_passing": self.is_passing()
        }


@dataclass
class SecurityReport:
    """
    Security analysis results for components and dependencies.
    """
    
    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Scope
    component_name: str = ""
    dependencies_scanned: List[str] = field(default_factory=list)
    
    # Results
    security_level: SecurityLevel = SecurityLevel.SAFE
    vulnerabilities_found: int = 0
    critical_issues: int = 0
    high_issues: int = 0
    medium_issues: int = 0
    low_issues: int = 0
    
    # Details
    vulnerability_details: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # License Analysis
    license_issues: List[str] = field(default_factory=list)
    license_compatibility: bool = True
    
    def get_risk_score(self) -> float:
        """Calculate overall risk score (0-1, higher is riskier)"""
        weights = {"critical": 1.0, "high": 0.7, "medium": 0.4, "low": 0.1}
        total_score = (
            self.critical_issues * weights["critical"] +
            self.high_issues * weights["high"] +
            self.medium_issues * weights["medium"] +
            self.low_issues * weights["low"]
        )
        # Normalize to 0-1 scale (assuming max 10 critical issues = 1.0 risk)
        return min(total_score / 10.0, 1.0)
    
    def is_safe_to_use(self) -> bool:
        """Determine if component is safe to use"""
        return (
            self.critical_issues == 0 and
            self.high_issues <= 2 and
            self.license_compatibility and
            len(self.license_issues) == 0
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "component_name": self.component_name,
            "security_level": self.security_level.value,
            "vulnerabilities_found": self.vulnerabilities_found,
            "critical_issues": self.critical_issues,
            "high_issues": self.high_issues,
            "risk_score": self.get_risk_score(),
            "is_safe_to_use": self.is_safe_to_use(),
            "license_compatibility": self.license_compatibility
        }


# ============================================================================
# Code Generation & Assembly Module Data Models
# ============================================================================

@dataclass
class ComponentAPI:
    """
    Represents the API surface of a component extracted via Tree-Sitter analysis.

    Used by ComponentAPIAnalyzer to understand how components can be integrated.
    """

    # Identity
    component_name: str = ""
    language: str = ""
    analysis_timestamp: datetime = field(default_factory=datetime.now)

    # API Surface Analysis
    public_methods: List[Dict[str, Any]] = field(default_factory=list)  # Method signatures
    exported_classes: List[Dict[str, Any]] = field(default_factory=list)  # Class definitions
    exported_functions: List[Dict[str, Any]] = field(default_factory=list)  # Function signatures

    # Module Structure
    imports: List[Dict[str, str]] = field(default_factory=list)  # Import statements
    exports: List[Dict[str, str]] = field(default_factory=list)  # Export statements
    dependencies: List[str] = field(default_factory=list)  # External dependencies

    # Configuration Patterns
    config_options: Dict[str, Any] = field(default_factory=dict)  # Configuration parameters
    initialization_patterns: List[str] = field(default_factory=list)  # How to initialize

    # Integration Metadata
    entry_points: List[str] = field(default_factory=list)  # Main entry points
    api_patterns: List[str] = field(default_factory=list)  # Detected patterns (REST, GraphQL, etc.)

    def get_complexity_score(self) -> float:
        """Calculate API complexity score (0-1)"""
        method_count = len(self.public_methods)
        class_count = len(self.exported_classes)
        dependency_count = len(self.dependencies)

        # Weighted complexity calculation
        complexity = (method_count * 0.1 + class_count * 0.2 + dependency_count * 0.05)
        return min(complexity / 10.0, 1.0)  # Normalize to 0-1

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "component_name": self.component_name,
            "language": self.language,
            "analysis_timestamp": self.analysis_timestamp.isoformat(),
            "public_methods_count": len(self.public_methods),
            "exported_classes_count": len(self.exported_classes),
            "dependencies_count": len(self.dependencies),
            "complexity_score": self.get_complexity_score(),
            "entry_points": self.entry_points,
            "api_patterns": self.api_patterns
        }


@dataclass
class AdapterCode:
    """
    Represents generated adapter code between two components.

    Enhanced version of GeneratedCode specifically for component integration.
    """

    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)

    # Integration Context
    source_component: str = ""  # Source component name
    target_component: str = ""  # Target component name
    pattern_type: str = ""      # Architectural pattern being implemented
    integration_complexity: IntegrationComplexity = IntegrationComplexity.MEDIUM

    # Generated Content
    adapter_code: str = ""      # Main adapter implementation
    configuration_code: str = "" # Configuration/setup code
    test_code: str = ""         # Generated test suite
    documentation: str = ""     # Generated documentation

    # File Structure
    file_path: str = ""         # Primary adapter file path
    additional_files: Dict[str, str] = field(default_factory=dict)  # path -> content

    # Technical Details
    language: str = "python"
    dependencies: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)

    # Quality Metrics
    lines_of_code: int = 0
    test_coverage: float = 0.0
    complexity_score: float = 0.0

    # Generation Metadata
    generation_model: str = ""  # Which LLM model was used
    generation_tokens: int = 0  # Token usage
    generation_time_seconds: float = 0.0

    def calculate_metrics(self) -> None:
        """Calculate code quality metrics"""
        self.lines_of_code = len([
            line for line in self.adapter_code.split('\n')
            if line.strip() and not line.strip().startswith('#')
        ])

        # TODO: REPLACE_MOCK - Implement real complexity analysis
        self.complexity_score = min(self.lines_of_code / 200, 1.0)

    def get_total_files(self) -> int:
        """Get total number of generated files"""
        return 1 + len(self.additional_files)  # Main file + additional files

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "id": self.id,
            "source_component": self.source_component,
            "target_component": self.target_component,
            "pattern_type": self.pattern_type,
            "integration_complexity": self.integration_complexity.value,
            "file_path": self.file_path,
            "language": self.language,
            "lines_of_code": self.lines_of_code,
            "test_coverage": self.test_coverage,
            "complexity_score": self.complexity_score,
            "total_files": self.get_total_files(),
            "dependencies": self.dependencies,
            "generation_model": self.generation_model,
            "generation_tokens": self.generation_tokens
        }


@dataclass
class ValidationResult:
    """
    Results from validating generated code through the ValidationPipeline.

    Implements quality-first generation with comprehensive validation gates.
    """

    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)

    # Validation Context
    code_id: str = ""           # ID of the code being validated
    validation_type: str = ""   # "syntax", "compilation", "tests", "security"

    # Results
    status: ValidationStatus = ValidationStatus.PENDING
    passed: bool = False

    # Detailed Results
    syntax_valid: bool = False
    compilation_successful: bool = False
    tests_passed: bool = False
    security_scan_passed: bool = False

    # Metrics
    test_pass_rate: float = 0.0
    code_coverage: float = 0.0
    performance_score: float = 0.0

    # Issues Found
    syntax_errors: List[str] = field(default_factory=list)
    compilation_errors: List[str] = field(default_factory=list)
    test_failures: List[str] = field(default_factory=list)
    security_issues: List[str] = field(default_factory=list)

    # Execution Details
    execution_time_seconds: float = 0.0
    memory_usage_mb: float = 0.0

    # Suggestions for Improvement
    improvement_suggestions: List[str] = field(default_factory=list)

    def get_overall_score(self) -> float:
        """Calculate overall validation score (0-1)"""
        weights = {
            "syntax": 0.2,
            "compilation": 0.3,
            "tests": 0.3,
            "security": 0.2
        }

        score = 0.0
        if self.syntax_valid:
            score += weights["syntax"]
        if self.compilation_successful:
            score += weights["compilation"]
        if self.tests_passed:
            score += weights["tests"] * self.test_pass_rate
        if self.security_scan_passed:
            score += weights["security"]

        return score

    def is_production_ready(self) -> bool:
        """Determine if code meets production quality standards"""
        return (
            self.syntax_valid and
            self.compilation_successful and
            self.test_pass_rate >= 0.8 and
            self.security_scan_passed and
            len(self.security_issues) == 0
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "id": self.id,
            "code_id": self.code_id,
            "validation_type": self.validation_type,
            "status": self.status.value,
            "passed": self.passed,
            "overall_score": self.get_overall_score(),
            "is_production_ready": self.is_production_ready(),
            "syntax_valid": self.syntax_valid,
            "compilation_successful": self.compilation_successful,
            "tests_passed": self.tests_passed,
            "test_pass_rate": self.test_pass_rate,
            "code_coverage": self.code_coverage,
            "security_scan_passed": self.security_scan_passed,
            "execution_time_seconds": self.execution_time_seconds,
            "issues_count": len(self.syntax_errors + self.compilation_errors +
                              self.test_failures + self.security_issues)
        }


@dataclass
class ProjectStructure:
    """
    Represents the generated project structure and organization.

    Used by CodeGenerationOrchestrator to manage the complete project layout.
    """

    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)

    # Project Metadata
    project_name: str = ""
    framework: str = ""         # Primary framework (FastAPI, Express, etc.)
    language: str = ""          # Primary language

    # File Structure
    files: Dict[str, str] = field(default_factory=dict)  # file_path -> content
    directories: List[str] = field(default_factory=list)  # Directory structure

    # Configuration Files
    package_config: Dict[str, Any] = field(default_factory=dict)  # package.json, requirements.txt
    docker_config: Dict[str, str] = field(default_factory=dict)   # Dockerfile, docker-compose
    env_config: Dict[str, str] = field(default_factory=dict)      # Environment variables

    # Documentation
    readme_content: str = ""
    api_documentation: str = ""
    deployment_guide: str = ""

    # Quality Metrics
    total_files: int = 0
    total_lines_of_code: int = 0
    test_files_count: int = 0
    documentation_coverage: float = 0.0

    def calculate_metrics(self) -> None:
        """Calculate project structure metrics"""
        self.total_files = len(self.files)

        # Calculate total lines of code, handling potential async mock objects
        total_lines = 0
        for content in self.files.values():
            try:
                # Check if content is a string
                if isinstance(content, str):
                    total_lines += len([line for line in content.split('\n') if line.strip()])
                elif hasattr(content, '__call__'):
                    # Handle mock objects or callable content
                    total_lines += 10  # Default line count for mocks
                else:
                    # Convert to string if possible
                    str_content = str(content)
                    if str_content and str_content != content:
                        total_lines += len([line for line in str_content.split('\n') if line.strip()])
                    else:
                        total_lines += 10  # Default fallback
            except Exception:
                # Fallback for any unexpected content types
                total_lines += 10

        self.total_lines_of_code = total_lines
        self.test_files_count = len([
            path for path in self.files.keys()
            if 'test' in path.lower() or path.endswith('_test.py') or path.endswith('.test.js')
        ])

        # TODO: REPLACE_MOCK - Implement real documentation coverage analysis
        self.documentation_coverage = 0.8 if self.readme_content else 0.3

    def get_file_tree(self) -> Dict[str, Any]:
        """Generate hierarchical file tree structure"""
        tree = {}
        for file_path in self.files.keys():
            parts = file_path.split('/')
            current = tree
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = "file"
        return tree

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "id": self.id,
            "project_name": self.project_name,
            "framework": self.framework,
            "language": self.language,
            "total_files": self.total_files,
            "total_lines_of_code": self.total_lines_of_code,
            "test_files_count": self.test_files_count,
            "documentation_coverage": self.documentation_coverage,
            "directories": self.directories,
            "file_tree": self.get_file_tree()
        }


@dataclass
class GenerationContext:
    """
    Context information for code generation operations.

    Used by ContextWeaver v2 to build intelligent prompts with component awareness.
    """

    # Generation Request
    goal: str = ""              # High-level generation goal
    generation_type: CodeGenerationType = CodeGenerationType.ADAPTER

    # Component Context
    source_component_api: Optional[ComponentAPI] = None
    target_component_api: Optional[ComponentAPI] = None
    selected_pattern: str = ""  # Architectural pattern

    # Project Context
    project_framework: str = ""
    project_language: str = ""
    existing_structure: Optional[ProjectStructure] = None

    # Knowledge Base Context
    similar_examples: List[Dict[str, Any]] = field(default_factory=list)  # RAG results
    pattern_templates: List[str] = field(default_factory=list)  # Template patterns

    # Generation Preferences
    preferred_model: str = "auto"  # LLM model preference
    quality_level: str = "production"  # "prototype", "production", "enterprise"
    include_tests: bool = True
    include_documentation: bool = True

    # Constraints
    max_complexity: IntegrationComplexity = IntegrationComplexity.HIGH
    required_dependencies: List[str] = field(default_factory=list)
    forbidden_dependencies: List[str] = field(default_factory=list)

    def get_context_richness_score(self) -> float:
        """Calculate how much context is available (0-1)"""
        score = 0.0

        # Component API context
        if self.source_component_api:
            score += 0.3
        if self.target_component_api:
            score += 0.3

        # Knowledge base context
        if self.similar_examples:
            score += 0.2
        if self.pattern_templates:
            score += 0.1

        # Project context
        if self.existing_structure:
            score += 0.1

        return score

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "goal": self.goal,
            "generation_type": self.generation_type.value,
            "selected_pattern": self.selected_pattern,
            "project_framework": self.project_framework,
            "project_language": self.project_language,
            "context_richness_score": self.get_context_richness_score(),
            "similar_examples_count": len(self.similar_examples),
            "pattern_templates_count": len(self.pattern_templates),
            "preferred_model": self.preferred_model,
            "quality_level": self.quality_level,
            "include_tests": self.include_tests,
            "include_documentation": self.include_documentation
        }
