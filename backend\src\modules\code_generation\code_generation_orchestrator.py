"""
CodeGenerationOrchestrator - Complete workflow coordination and project assembly.

Orchestrates all components in the Code Generation & Assembly Module to provide
end-to-end code generation workflows with quality-first generation, comprehensive
validation, and production-ready output.

Key Features:
- Complete workflow coordination between all components
- Project structure generation and assembly
- Progress tracking and error recovery
- Quality gates and validation integration
- TaskExecutionAgent-native implementation
"""

import logging
import asyncio
import os
import shutil
import zipfile
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from enum import Enum
from pathlib import Path

from ...state.components import (
    AdapterCode, ValidationResult, ValidationStatus, ComponentTestResult,
    ComponentTestStatus, GenerationContext, CodeGenerationType, ProjectStructure
)
from ...state.project_state import ProjectState
from .component_api_analyzer import ComponentAPIAnalyzer
from .context_weaver import ContextWeaver
from .specialized_llm_client import SpecializedLLMClient
from .adapter_generator import AdapterGenerator
from .test_generation_engine import CodeTestGenerationEngine
from .validation_pipeline import Validation<PERSON><PERSON>eline, ValidationGate

logger = logging.getLogger(__name__)


class OrchestrationPhase(Enum):
    """Phases of code generation orchestration"""
    INITIALIZATION = "initialization"
    COMPONENT_ANALYSIS = "component_analysis"
    CONTEXT_BUILDING = "context_building"
    ADAPTER_GENERATION = "adapter_generation"
    TEST_GENERATION = "test_generation"
    VALIDATION = "validation"
    PROJECT_ASSEMBLY = "project_assembly"
    FINALIZATION = "finalization"


class GenerationStrategy(Enum):
    """Code generation strategies"""
    QUALITY_FIRST = "quality_first"
    SPEED_OPTIMIZED = "speed_optimized"
    COMPREHENSIVE = "comprehensive"
    MINIMAL_VIABLE = "minimal_viable"


class CodeGenerationOrchestrator:
    """
    Complete workflow coordination for code generation and assembly.
    
    Orchestrates all components to provide end-to-end code generation
    with quality-first approach and production-ready output.
    """
    
    def __init__(self, 
                 api_analyzer: Optional[ComponentAPIAnalyzer] = None,
                 context_weaver: Optional[ContextWeaver] = None,
                 llm_client: Optional[SpecializedLLMClient] = None,
                 adapter_generator: Optional[AdapterGenerator] = None,
                 test_engine: Optional[CodeTestGenerationEngine] = None,
                 validation_pipeline: Optional[ValidationPipeline] = None):
        """Initialize CodeGenerationOrchestrator with all component dependencies"""
        
        # TODO: REPLACE_MOCK - Real integration with all components
        self.api_analyzer = api_analyzer or ComponentAPIAnalyzer()
        self.context_weaver = context_weaver or ContextWeaver()
        self.llm_client = llm_client or SpecializedLLMClient()
        self.adapter_generator = adapter_generator or AdapterGenerator()
        self.test_engine = test_engine or CodeTestGenerationEngine()
        self.validation_pipeline = validation_pipeline or ValidationPipeline()
        
        # Orchestration configuration
        self.generation_strategies = self._initialize_generation_strategies()
        
        # Quality gates configuration
        self.quality_gates = {
            "syntax_validation": True,
            "compilation_check": True,
            "test_execution": True,
            "security_scan": True,
            "performance_check": False,  # Optional for speed
            "best_practices": False,     # Optional for speed
            "production_readiness": True
        }
        
        # Orchestration statistics
        self.orchestration_stats = {
            "total_orchestrations": 0,
            "successful_orchestrations": 0,
            "phase_failure_counts": {},
            "average_orchestration_time": 0.0,
            "strategy_usage": {},
            "quality_scores": []
        }
        
        # Project templates
        self.project_templates = self._initialize_project_templates()
        
        # Error recovery strategies
        self.recovery_strategies = self._initialize_recovery_strategies()
    
    def _initialize_generation_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize code generation strategy configurations"""
        return {
            "quality_first": {
                "description": "Maximum quality with comprehensive validation",
                "validation_gates": ["syntax", "compilation", "tests", "security", "best_practices", "production_readiness"],
                "test_coverage_target": 0.90,
                "performance_optimization": True,
                "documentation_generation": True,
                "estimated_time_multiplier": 1.5
            },
            "speed_optimized": {
                "description": "Fast generation with essential validation only",
                "validation_gates": ["syntax", "compilation", "tests"],
                "test_coverage_target": 0.70,
                "performance_optimization": False,
                "documentation_generation": False,
                "estimated_time_multiplier": 0.7
            },
            "comprehensive": {
                "description": "Complete generation with all features enabled",
                "validation_gates": ["syntax", "compilation", "tests", "security", "performance", "best_practices", "production_readiness"],
                "test_coverage_target": 0.95,
                "performance_optimization": True,
                "documentation_generation": True,
                "estimated_time_multiplier": 2.0
            },
            "minimal_viable": {
                "description": "Basic working code with minimal validation",
                "validation_gates": ["syntax", "compilation"],
                "test_coverage_target": 0.50,
                "performance_optimization": False,
                "documentation_generation": False,
                "estimated_time_multiplier": 0.5
            }
        }
    
    def _initialize_project_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize project structure templates"""
        return {
            "python": {
                "structure": {
                    "src/": {"type": "directory"},
                    "src/__init__.py": {"type": "file", "content": "# Generated by CodeQuilter\n"},
                    "tests/": {"type": "directory"},
                    "tests/__init__.py": {"type": "file", "content": ""},
                    "requirements.txt": {"type": "file", "content": "# Generated requirements\n"},
                    "README.md": {"type": "file", "template": "python_readme"},
                    ".gitignore": {"type": "file", "template": "python_gitignore"},
                    "setup.py": {"type": "file", "template": "python_setup"}
                },
                "entry_point": "src/main.py"
            },
            "javascript": {
                "structure": {
                    "src/": {"type": "directory"},
                    "src/index.js": {"type": "file", "template": "js_entry"},
                    "tests/": {"type": "directory"},
                    "package.json": {"type": "file", "template": "package_json"},
                    "README.md": {"type": "file", "template": "js_readme"},
                    ".gitignore": {"type": "file", "template": "js_gitignore"},
                    "jest.config.js": {"type": "file", "template": "jest_config"}
                },
                "entry_point": "src/index.js"
            },
            "java": {
                "structure": {
                    "src/main/java/": {"type": "directory"},
                    "src/test/java/": {"type": "directory"},
                    "pom.xml": {"type": "file", "template": "maven_pom"},
                    "README.md": {"type": "file", "template": "java_readme"},
                    ".gitignore": {"type": "file", "template": "java_gitignore"}
                },
                "entry_point": "src/main/java/Main.java"
            }
        }
    
    def _initialize_recovery_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize error recovery strategies"""
        return {
            "component_analysis_failure": {
                "retry_count": 2,
                "fallback_action": "use_mock_api_data",
                "recovery_time": 30
            },
            "adapter_generation_failure": {
                "retry_count": 3,
                "fallback_action": "use_template_based_generation",
                "recovery_time": 60
            },
            "test_generation_failure": {
                "retry_count": 2,
                "fallback_action": "generate_basic_tests",
                "recovery_time": 45
            },
            "validation_failure": {
                "retry_count": 1,
                "fallback_action": "skip_optional_gates",
                "recovery_time": 30
            }
        }

    async def orchestrate_code_generation(self, project_state: ProjectState,
                                         strategy: GenerationStrategy = GenerationStrategy.QUALITY_FIRST,
                                         progress_callback: Optional[callable] = None) -> Tuple[ProjectStructure, List[str]]:
        """
        Orchestrate complete code generation workflow.

        Args:
            project_state: Current project state with selected components
            strategy: Generation strategy to use
            progress_callback: Optional callback for progress updates

        Returns:
            Tuple of (ProjectStructure, suggested_actions)
        """
        start_time = datetime.now()

        try:
            # Initialize orchestration
            strategy_config = self.generation_strategies.get(strategy.value, {})
            logger.info(f"Starting code generation orchestration with {strategy.value} strategy")

            if progress_callback:
                await progress_callback(OrchestrationPhase.INITIALIZATION, "Initializing code generation workflow")

            # Phase 1: Component Analysis
            if progress_callback:
                await progress_callback(OrchestrationPhase.COMPONENT_ANALYSIS, "Analyzing component APIs")

            component_apis = await self._analyze_components(project_state)

            # Phase 2: Context Building
            if progress_callback:
                await progress_callback(OrchestrationPhase.CONTEXT_BUILDING, "Building generation context")

            generation_context = await self._build_generation_context(project_state, component_apis, strategy_config)

            # Phase 3: Adapter Generation
            if progress_callback:
                await progress_callback(OrchestrationPhase.ADAPTER_GENERATION, "Generating adapter code")

            adapters = await self._generate_adapters(generation_context, strategy_config)

            # Phase 4: Test Generation
            if progress_callback:
                await progress_callback(OrchestrationPhase.TEST_GENERATION, "Generating test suites")

            test_results = await self._generate_tests(adapters, generation_context, strategy_config)

            # Phase 5: Validation
            if progress_callback:
                await progress_callback(OrchestrationPhase.VALIDATION, "Validating generated code")

            validation_results = await self._validate_code(adapters, project_state, strategy_config)

            # Phase 6: Project Assembly
            if progress_callback:
                await progress_callback(OrchestrationPhase.PROJECT_ASSEMBLY, "Assembling project structure")

            project_structure = await self._assemble_project(
                project_state, adapters, test_results, validation_results, strategy_config
            )

            # Phase 7: Finalization
            if progress_callback:
                await progress_callback(OrchestrationPhase.FINALIZATION, "Finalizing project")

            suggested_actions = self._generate_suggested_actions(
                project_structure, validation_results, strategy_config
            )

            # Track statistics
            orchestration_time = (datetime.now() - start_time).total_seconds()
            self._track_orchestration_stats(strategy, True, orchestration_time, validation_results)

            logger.info(f"Code generation orchestration completed successfully in {orchestration_time:.2f}s")
            return project_structure, suggested_actions

        except Exception as e:
            logger.error(f"Code generation orchestration failed: {str(e)}")
            orchestration_time = (datetime.now() - start_time).total_seconds()
            self._track_orchestration_stats(strategy, False, orchestration_time, [])

            # Return minimal project structure on failure
            fallback_structure = await self._create_fallback_project_structure(project_state)
            fallback_actions = [
                "Review orchestration error and retry",
                "Consider using a simpler generation strategy",
                "Check component selection and project configuration"
            ]
            return fallback_structure, fallback_actions

    async def _analyze_components(self, project_state: ProjectState) -> Dict[str, Any]:
        """Analyze selected components to extract API information"""
        component_apis = {}

        try:
            for component in project_state.selected_components:
                logger.debug(f"Analyzing component: {component.name}")

                # Use ComponentAPIAnalyzer to extract API information
                api_info = await self.api_analyzer.analyze_component_api(component)
                component_apis[component.name] = api_info

        except Exception as e:
            logger.warning(f"Component analysis failed: {str(e)}")
            # Use fallback mock data
            component_apis = self._create_fallback_component_apis(project_state.selected_components)

        return component_apis

    async def _build_generation_context(self, project_state: ProjectState,
                                      component_apis: Dict[str, Any],
                                      strategy_config: Dict[str, Any]) -> GenerationContext:
        """Build comprehensive generation context"""
        try:
            # Determine primary language and framework
            primary_language = self._determine_primary_language(project_state, component_apis)
            primary_framework = self._determine_primary_framework(project_state, component_apis)

            # Build context using ContextWeaver
            context = await self.context_weaver.build_generation_context(
                goal=f"Generate {project_state.project_name} with selected components",
                generation_type=CodeGenerationType.ADAPTER,
                source_component_api=None,  # Will be set per adapter
                target_component_api=None,  # Will be set per adapter
                selected_pattern=project_state.target_patterns[0] if project_state.target_patterns else "integration",
                project_framework=primary_framework,
                project_language=primary_language,
                quality_level=strategy_config.get("description", "production"),
                include_tests=strategy_config.get("test_coverage_target", 0.8) > 0.5,
                include_documentation=strategy_config.get("documentation_generation", True)
            )

            return context

        except Exception as e:
            logger.warning(f"Context building failed: {str(e)}")
            # Return minimal context
            return GenerationContext(
                goal=f"Generate {project_state.project_name}",
                generation_type=CodeGenerationType.ADAPTER,
                project_language="python",
                project_framework="fastapi"
            )

    async def _generate_adapters(self, context: GenerationContext,
                               strategy_config: Dict[str, Any]) -> List[AdapterCode]:
        """Generate adapter code for component integrations"""
        adapters = []

        try:
            # TODO: REPLACE_MOCK - Real adapter generation based on component pairs
            # For now, generate a sample adapter
            adapter = await self.adapter_generator.generate_adapter(
                source_component="auth-service",
                target_component="user-service",
                pattern_type=context.selected_pattern,
                context=context
            )
            adapters.append(adapter)

        except Exception as e:
            logger.warning(f"Adapter generation failed: {str(e)}")
            # Create fallback adapter
            fallback_adapter = self._create_fallback_adapter(context)
            adapters.append(fallback_adapter)

        return adapters

    async def _generate_tests(self, adapters: List[AdapterCode],
                            context: GenerationContext,
                            strategy_config: Dict[str, Any]) -> List[ComponentTestResult]:
        """Generate comprehensive test suites for adapters"""
        test_results = []

        try:
            coverage_target = strategy_config.get("test_coverage_target", 0.8)

            for adapter in adapters:
                test_result = await self.test_engine.generate_test_suite(
                    adapter_code=adapter,
                    context=context,
                    coverage_target=coverage_target
                )
                test_results.append(test_result)

        except Exception as e:
            logger.warning(f"Test generation failed: {str(e)}")
            # Create fallback test results
            for adapter in adapters:
                fallback_test = self._create_fallback_test_result(adapter)
                test_results.append(fallback_test)

        return test_results

    async def _validate_code(self, adapters: List[AdapterCode],
                           project_state: ProjectState,
                           strategy_config: Dict[str, Any]) -> List[ValidationResult]:
        """Validate generated code through comprehensive pipeline"""
        validation_results = []

        try:
            # Get validation gates from strategy
            validation_gates = strategy_config.get("validation_gates", ["syntax", "compilation", "tests"])
            gates = [ValidationGate(gate) for gate in validation_gates if hasattr(ValidationGate, gate.upper())]

            for adapter in adapters:
                validation_result = await self.validation_pipeline.validate_code(
                    adapter_code=adapter,
                    project_state=project_state,
                    gates=gates
                )
                validation_results.append(validation_result)

        except Exception as e:
            logger.warning(f"Code validation failed: {str(e)}")
            # Create fallback validation results
            for adapter in adapters:
                fallback_validation = self._create_fallback_validation_result(adapter)
                validation_results.append(fallback_validation)

        return validation_results

    async def _assemble_project(self, project_state: ProjectState,
                              adapters: List[AdapterCode],
                              test_results: List[ComponentTestResult],
                              validation_results: List[ValidationResult],
                              strategy_config: Dict[str, Any]) -> ProjectStructure:
        """Assemble complete project structure"""
        try:
            # Determine project language and framework
            primary_language = self._determine_primary_language(project_state, {})
            primary_framework = self._determine_primary_framework(project_state, {})

            # Create project structure
            project_structure = ProjectStructure(
                project_name=project_state.project_name,
                framework=primary_framework,
                language=primary_language
            )

            # Get project template
            template = self.project_templates.get(primary_language, self.project_templates["python"])

            # Create base project structure
            for path, config in template["structure"].items():
                if config["type"] == "file":
                    content = self._generate_file_content(path, config, project_state, strategy_config)
                    project_structure.files[path] = content
                elif config["type"] == "directory":
                    project_structure.directories.append(path)

            # Add adapter files
            for adapter in adapters:
                adapter_path = f"src/adapters/{adapter.file_path}"
                project_structure.files[adapter_path] = adapter.adapter_code

                # Add test files
                if adapter.test_code:
                    test_path = f"tests/test_{adapter.file_path}"
                    project_structure.files[test_path] = adapter.test_code

            # Add configuration files
            self._add_configuration_files(project_structure, adapters, strategy_config)

            # Add documentation
            if strategy_config.get("documentation_generation", True):
                self._add_documentation_files(project_structure, adapters, validation_results)

            # Calculate metrics
            project_structure.calculate_metrics()

            return project_structure

        except Exception as e:
            logger.warning(f"Project assembly failed: {str(e)}")
            return await self._create_fallback_project_structure(project_state)

    def _generate_suggested_actions(self, project_structure: ProjectStructure,
                                  validation_results: List[ValidationResult],
                                  strategy_config: Dict[str, Any]) -> List[str]:
        """Generate suggested next actions based on results"""
        actions = []

        # Check validation results
        failed_validations = [vr for vr in validation_results if not vr.passed]
        if failed_validations:
            actions.append("Review and fix validation failures before deployment")
            actions.append("Run validation pipeline again after fixes")

        # Check test coverage
        avg_coverage = sum(vr.code_coverage for vr in validation_results) / len(validation_results) if validation_results else 0
        target_coverage = strategy_config.get("test_coverage_target", 0.8)
        if avg_coverage < target_coverage:
            actions.append(f"Improve test coverage from {avg_coverage:.1%} to {target_coverage:.1%}")

        # Check project completeness
        if project_structure.total_files < 5:
            actions.append("Consider adding more project structure files")

        if not project_structure.readme_content:
            actions.append("Add comprehensive README documentation")

        # Strategy-specific actions
        if strategy_config.get("performance_optimization", False):
            actions.append("Run performance optimization analysis")

        if strategy_config.get("documentation_generation", True):
            actions.append("Generate API documentation")

        # Default actions
        if not actions:
            actions.extend([
                "Review generated code and project structure",
                "Run tests to verify functionality",
                "Deploy to development environment for testing"
            ])

        return actions

    def _determine_primary_language(self, project_state: ProjectState, component_apis: Dict[str, Any]) -> str:
        """Determine primary programming language for the project"""
        # TODO: REPLACE_MOCK - Real language detection based on components
        # For now, default to Python
        return "python"

    def _determine_primary_framework(self, project_state: ProjectState, component_apis: Dict[str, Any]) -> str:
        """Determine primary framework for the project"""
        # TODO: REPLACE_MOCK - Real framework detection based on patterns and components
        # For now, default to FastAPI for Python projects
        return "fastapi"

    def _generate_file_content(self, path: str, config: Dict[str, Any],
                             project_state: ProjectState, strategy_config: Dict[str, Any]) -> str:
        """Generate content for project files"""
        if "content" in config:
            return config["content"]

        template = config.get("template", "")

        # TODO: REPLACE_MOCK - Real template-based file generation
        if template == "python_readme":
            return f"""# {project_state.project_name}

{project_state.project_brief}

## Generated by CodeQuilter

This project was generated using CodeQuilter's {strategy_config.get('description', 'quality-first')} strategy.

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```python
# TODO: Add usage examples
```

## Testing

```bash
pytest tests/
```
"""
        elif template == "package_json":
            return f"""{{
  "name": "{project_state.project_name.lower().replace(' ', '-')}",
  "version": "1.0.0",
  "description": "{project_state.project_brief}",
  "main": "src/index.js",
  "scripts": {{
    "start": "node src/index.js",
    "test": "jest",
    "dev": "nodemon src/index.js"
  }},
  "dependencies": {{}},
  "devDependencies": {{
    "jest": "^29.0.0",
    "nodemon": "^2.0.0"
  }}
}}
"""

        return f"# Generated file: {path}\n# TODO: Add content\n"

    def _add_configuration_files(self, project_structure: ProjectStructure,
                               adapters: List[AdapterCode], strategy_config: Dict[str, Any]) -> None:
        """Add configuration files to project structure"""
        # Add environment configuration
        env_content = """# Environment Configuration
# Generated by CodeQuilter

# Application Settings
APP_NAME={project_name}
APP_VERSION=1.0.0
DEBUG=true

# Database Settings
# DATABASE_URL=postgresql://user:password@localhost/dbname

# API Settings
# API_KEY=your_api_key_here
""".format(project_name=project_structure.project_name)

        project_structure.env_config[".env.example"] = env_content

        # Add Docker configuration if comprehensive strategy
        if strategy_config.get("description") == "Complete generation with all features enabled":
            dockerfile_content = """FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY tests/ ./tests/

EXPOSE 8000

CMD ["python", "-m", "src.main"]
"""
            project_structure.docker_config["Dockerfile"] = dockerfile_content

    def _add_documentation_files(self, project_structure: ProjectStructure,
                               adapters: List[AdapterCode],
                               validation_results: List[ValidationResult]) -> None:
        """Add documentation files to project structure"""
        # Generate API documentation
        api_docs = """# API Documentation

## Generated Adapters

"""
        for adapter in adapters:
            api_docs += f"""### {adapter.source_component} → {adapter.target_component}

**Pattern**: {adapter.pattern_type}
**File**: `{adapter.file_path}`
**Language**: {adapter.language}

"""

        project_structure.api_documentation = api_docs

        # Generate deployment guide
        deployment_guide = """# Deployment Guide

## Prerequisites

- Python 3.11+
- Required dependencies (see requirements.txt)

## Development Deployment

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run tests:
   ```bash
   pytest tests/
   ```

3. Start application:
   ```bash
   python -m src.main
   ```

## Production Deployment

1. Build Docker image:
   ```bash
   docker build -t {project_name} .
   ```

2. Run container:
   ```bash
   docker run -p 8000:8000 {project_name}
   ```
""".format(project_name=project_structure.project_name.lower().replace(' ', '-'))

        project_structure.deployment_guide = deployment_guide

    def _create_fallback_component_apis(self, components: List[Any]) -> Dict[str, Any]:
        """Create fallback component API data"""
        # TODO: REPLACE_MOCK - Real fallback API generation
        return {component.name: {"mock": True, "api_methods": []} for component in components}

    def _create_fallback_adapter(self, context: GenerationContext) -> AdapterCode:
        """Create fallback adapter code"""
        from ...state.components import AdapterCode, IntegrationComplexity

        return AdapterCode(
            source_component="fallback-source",
            target_component="fallback-target",
            pattern_type=context.selected_pattern,
            integration_complexity=IntegrationComplexity.LOW,
            adapter_code="""# Fallback adapter generated by CodeQuilter
class FallbackAdapter:
    def __init__(self):
        pass

    def process(self, data):
        # TODO: Implement adapter logic
        return data
""",
            test_code="""# Fallback test generated by CodeQuilter
import pytest

class TestFallbackAdapter:
    def test_process(self):
        adapter = FallbackAdapter()
        result = adapter.process("test")
        assert result == "test"
""",
            file_path="fallback_adapter.py",
            language=context.project_language
        )

    def _create_fallback_test_result(self, adapter: AdapterCode) -> ComponentTestResult:
        """Create fallback test result"""
        return ComponentTestResult(
            component_name=f"{adapter.source_component}-{adapter.target_component}",
            test_type="unit",
            status=ComponentTestStatus.PASSED,
            passed=1,
            failed=0,
            coverage_percentage=0.5
        )

    def _create_fallback_validation_result(self, adapter: AdapterCode) -> ValidationResult:
        """Create fallback validation result"""
        return ValidationResult(
            code_id=adapter.id,
            validation_type="fallback",
            status=ValidationStatus.PASSED,
            passed=True,
            syntax_valid=True,
            compilation_successful=True,
            tests_passed=True,
            test_pass_rate=1.0,
            code_coverage=0.5
        )

    async def _create_fallback_project_structure(self, project_state: ProjectState) -> ProjectStructure:
        """Create minimal fallback project structure"""
        structure = ProjectStructure(
            project_name=project_state.project_name,
            framework="fastapi",
            language="python"
        )

        # Add minimal files
        structure.files["src/__init__.py"] = "# Generated by CodeQuilter\n"
        structure.files["src/main.py"] = """# Main application file
# Generated by CodeQuilter

def main():
    print("Hello from CodeQuilter!")

if __name__ == "__main__":
    main()
"""
        structure.files["requirements.txt"] = "# Add your dependencies here\n"
        structure.files["README.md"] = f"# {project_state.project_name}\n\nGenerated by CodeQuilter\n"

        structure.directories = ["src/", "tests/"]
        structure.calculate_metrics()

        return structure

    def _track_orchestration_stats(self, strategy: GenerationStrategy, success: bool,
                                 orchestration_time: float, validation_results: List[ValidationResult]) -> None:
        """Track orchestration statistics"""
        self.orchestration_stats["total_orchestrations"] += 1

        if success:
            self.orchestration_stats["successful_orchestrations"] += 1

        # Track strategy usage
        strategy_key = strategy.value
        if strategy_key not in self.orchestration_stats["strategy_usage"]:
            self.orchestration_stats["strategy_usage"][strategy_key] = 0
        self.orchestration_stats["strategy_usage"][strategy_key] += 1

        # Track quality scores
        if validation_results:
            avg_quality = sum(vr.get_overall_score() for vr in validation_results) / len(validation_results)
            self.orchestration_stats["quality_scores"].append(avg_quality)

        # Update average orchestration time
        total = self.orchestration_stats["total_orchestrations"]
        current_avg = self.orchestration_stats["average_orchestration_time"]
        self.orchestration_stats["average_orchestration_time"] = (
            (current_avg * (total - 1) + orchestration_time) / total
        )

    def get_orchestration_statistics(self) -> Dict[str, Any]:
        """Get comprehensive orchestration statistics"""
        stats = self.orchestration_stats.copy()

        # Add success rate
        total = stats["total_orchestrations"]
        successful = stats["successful_orchestrations"]
        stats["success_rate"] = successful / total if total > 0 else 0.0

        # Add average quality score
        quality_scores = stats["quality_scores"]
        stats["average_quality_score"] = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

        # Add strategy information
        stats["available_strategies"] = [strategy.value for strategy in GenerationStrategy]
        stats["orchestration_phases"] = [phase.value for phase in OrchestrationPhase]

        return stats
