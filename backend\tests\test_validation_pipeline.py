"""
Tests for ValidationPipeline - Quality gates and validation workflows for generated code.

Tests the comprehensive validation including syntax checking, compilation verification,
test execution, security scanning, and production readiness assessment.
"""

import pytest
import tempfile
import os
import shutil
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from backend.src.modules.code_generation.validation_pipeline import (
    ValidationPipeline, ValidationGate, SandboxEnvironment
)
from backend.src.modules.code_generation.test_generation_engine import CodeTestGenerationEngine
from backend.src.state.components import (
    AdapterCode, ValidationResult, ValidationStatus, ComponentTestResult,
    ComponentTestStatus, IntegrationComplexity
)
from backend.src.state.project_state import ProjectState, ProjectStatus


class TestValidationPipeline:
    """Test ValidationPipeline functionality"""
    
    @pytest.fixture
    def mock_test_engine(self):
        """Create mock CodeTestGenerationEngine"""
        mock = Mock(spec=CodeTestGenerationEngine)
        return mock
    
    @pytest.fixture
    def validation_pipeline(self, mock_test_engine):
        """Create ValidationPipeline instance for testing"""
        return ValidationPipeline(test_engine=mock_test_engine)
    
    @pytest.fixture
    def sample_adapter_code(self):
        """Create sample adapter code for testing"""
        return AdapterCode(
            source_component="auth-service",
            target_component="user-service",
            pattern_type="authentication",
            integration_complexity=IntegrationComplexity.MEDIUM,
            adapter_code='''
class AuthAdapter:
    """Authentication adapter for user service integration."""
    
    def __init__(self, auth_service, user_service):
        self.auth_service = auth_service
        self.user_service = user_service
    
    def authenticate(self, token: str) -> bool:
        """Authenticate user with token."""
        try:
            if self.auth_service.validate_token(token):
                return True
            return False
        except Exception as e:
            raise AuthenticationError(f"Authentication failed: {e}")
    
    def get_user_info(self, token: str) -> dict:
        """Get user information if authenticated."""
        if self.authenticate(token):
            return self.user_service.get_user_by_token(token)
        return None
''',
            test_code='''
import pytest
from unittest.mock import Mock

class TestAuthAdapter:
    @pytest.fixture
    def auth_adapter(self):
        auth_service = Mock()
        user_service = Mock()
        return AuthAdapter(auth_service, user_service)
    
    def test_authenticate_success(self, auth_adapter):
        auth_adapter.auth_service.validate_token.return_value = True
        result = auth_adapter.authenticate("valid_token")
        assert result is True
    
    def test_authenticate_failure(self, auth_adapter):
        auth_adapter.auth_service.validate_token.return_value = False
        result = auth_adapter.authenticate("invalid_token")
        assert result is False
    
    def test_get_user_info_success(self, auth_adapter):
        auth_adapter.auth_service.validate_token.return_value = True
        auth_adapter.user_service.get_user_by_token.return_value = {"id": 1, "name": "test"}
        result = auth_adapter.get_user_info("valid_token")
        assert result == {"id": 1, "name": "test"}
''',
            file_path="auth_adapter.py",
            language="python",
            test_coverage=0.85
        )
    
    @pytest.fixture
    def sample_project_state(self):
        """Create sample project state"""
        return ProjectState(
            session_id="test-session",
            project_name="Test Project",
            project_brief="Authentication integration project",
            target_patterns=["authentication"],
            status=ProjectStatus.QUILTING
        )
    
    def test_validation_gates_initialization(self, validation_pipeline):
        """Test validation gates are properly initialized"""
        gates = validation_pipeline.validation_gates
        
        # Verify all expected gates are present
        expected_gates = ["syntax", "compilation", "tests", "security", "performance", "best_practices", "production_readiness"]
        for gate in expected_gates:
            assert gate in gates
            
            # Verify gate structure
            gate_config = gates[gate]
            assert "enabled" in gate_config
            assert "timeout_seconds" in gate_config
            assert "required" in gate_config
            assert "description" in gate_config
    
    def test_sandbox_config_initialization(self, validation_pipeline):
        """Test sandbox configuration is properly initialized"""
        config = validation_pipeline.sandbox_config
        
        assert "default_environment" in config
        assert "timeout_seconds" in config
        assert "memory_limit_mb" in config
        assert "allowed_imports" in config
        assert "blocked_imports" in config
        
        # Verify security settings
        assert config["network_access"] is False
        assert "subprocess" in config["blocked_imports"]
        assert "eval" in config["blocked_imports"]
    
    def test_language_validators_initialization(self, validation_pipeline):
        """Test language validators are properly initialized"""
        validators = validation_pipeline.language_validators
        
        # Verify all expected languages are present
        expected_languages = ["python", "javascript", "java"]
        for language in expected_languages:
            assert language in validators
            
            # Verify validator structure
            validator_config = validators[language]
            assert "syntax_checker" in validator_config
            assert "linter" in validator_config
            assert "test_runner" in validator_config
            assert "file_extensions" in validator_config
    
    def test_security_scanners_initialization(self, validation_pipeline):
        """Test security scanners are properly initialized"""
        scanners = validation_pipeline.security_scanners
        
        # Verify language-specific scanners
        assert "python" in scanners
        assert "bandit" in scanners["python"]
        assert "safety" in scanners["python"]
        
        assert "javascript" in scanners
        assert "npm_audit" in scanners["javascript"]
    
    def test_quality_thresholds_initialization(self, validation_pipeline):
        """Test quality thresholds are properly set"""
        thresholds = validation_pipeline.quality_thresholds
        
        assert "min_test_coverage" in thresholds
        assert "max_complexity_score" in thresholds
        assert "max_security_issues" in thresholds
        assert "min_performance_score" in thresholds
        
        # Verify reasonable threshold values
        assert 0.0 <= thresholds["min_test_coverage"] <= 1.0
        assert thresholds["max_security_issues"] >= 0
    
    @pytest.mark.asyncio
    async def test_validate_code_success(self, validation_pipeline, sample_adapter_code, sample_project_state):
        """Test successful code validation"""
        result = await validation_pipeline.validate_code(
            sample_adapter_code,
            sample_project_state,
            gates=[ValidationGate.SYNTAX, ValidationGate.TESTS]
        )
        
        # Verify result structure
        assert isinstance(result, ValidationResult)
        assert result.code_id == sample_adapter_code.id
        assert result.validation_type == "comprehensive"
        assert result.status in [ValidationStatus.PASSED, ValidationStatus.FAILED]
        assert isinstance(result.passed, bool)
        assert hasattr(result, 'gate_results')
    
    @pytest.mark.asyncio
    async def test_validate_code_with_all_gates(self, validation_pipeline, sample_adapter_code, sample_project_state):
        """Test validation with all gates enabled"""
        result = await validation_pipeline.validate_code(
            sample_adapter_code,
            sample_project_state
        )
        
        assert isinstance(result, ValidationResult)
        assert result.code_id == sample_adapter_code.id
        
        # Should have results for multiple gates
        if hasattr(result, 'gate_results'):
            assert len(result.gate_results) > 0
    
    @pytest.mark.asyncio
    async def test_create_sandbox_environment(self, validation_pipeline, sample_adapter_code):
        """Test sandbox environment creation"""
        sandbox_path = await validation_pipeline._create_sandbox_environment(
            sample_adapter_code, SandboxEnvironment.PROCESS_ISOLATION
        )
        
        try:
            # Verify sandbox was created
            assert os.path.exists(sandbox_path)
            assert os.path.isdir(sandbox_path)
            
            # Verify adapter code file was created
            adapter_file = os.path.join(sandbox_path, "adapter.py")
            assert os.path.exists(adapter_file)
            
            # Verify test code file was created
            test_file = os.path.join(sandbox_path, "test_adapter.py")
            assert os.path.exists(test_file)
            
            # Verify project structure
            if sample_adapter_code.language.lower() == "python":
                init_file = os.path.join(sandbox_path, "__init__.py")
                assert os.path.exists(init_file)
                
        finally:
            # Clean up
            await validation_pipeline._cleanup_sandbox_environment(sandbox_path)
    
    @pytest.mark.asyncio
    async def test_validate_syntax_python_success(self, validation_pipeline, sample_adapter_code):
        """Test successful Python syntax validation"""
        result = await validation_pipeline._validate_syntax(sample_adapter_code, "/tmp", 30)
        
        assert result["passed"] is True
        assert "syntax validation passed" in result["message"].lower()
        assert "details" in result
        assert result["details"]["errors"] == []
    
    @pytest.mark.asyncio
    async def test_validate_syntax_python_failure(self, validation_pipeline):
        """Test Python syntax validation with syntax error"""
        invalid_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="def invalid_function(\n    # Missing closing parenthesis",
            language="python"
        )
        
        result = await validation_pipeline._validate_syntax(invalid_code, "/tmp", 30)
        
        assert result["passed"] is False
        assert "syntax error" in result["error"].lower()
        assert "details" in result

    @pytest.mark.asyncio
    async def test_validate_compilation_python_success(self, validation_pipeline, sample_adapter_code):
        """Test successful Python compilation validation"""
        result = await validation_pipeline._validate_compilation(sample_adapter_code, "/tmp", 60)

        assert result["passed"] is True
        assert "compilation successful" in result["message"].lower()

    @pytest.mark.asyncio
    async def test_validate_tests_success(self, validation_pipeline, sample_adapter_code):
        """Test successful test validation"""
        result = await validation_pipeline._validate_tests(sample_adapter_code, "/tmp", 120)

        assert result["passed"] is True
        assert "test validation passed" in result["message"].lower()
        assert "details" in result
        assert result["details"]["tests_run"] > 0
        assert result["details"]["coverage"] > 0

    @pytest.mark.asyncio
    async def test_validate_tests_no_test_code(self, validation_pipeline):
        """Test test validation with no test code"""
        adapter_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="def test_function(): pass",
            language="python"
            # No test_code provided
        )

        result = await validation_pipeline._validate_tests(adapter_code, "/tmp", 120)

        assert result["passed"] is False
        assert "no test code available" in result["error"].lower()
        assert result["details"]["coverage"] == 0.0

    @pytest.mark.asyncio
    async def test_validate_security_clean_code(self, validation_pipeline, sample_adapter_code):
        """Test security validation with clean code"""
        result = await validation_pipeline._validate_security(sample_adapter_code, "/tmp", 90)

        assert result["passed"] is True
        assert "details" in result
        assert result["details"]["total_issues"] == 0
        assert result["details"]["high_severity_count"] == 0

    @pytest.mark.asyncio
    async def test_validate_security_dangerous_code(self, validation_pipeline):
        """Test security validation with dangerous code"""
        dangerous_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="""
def dangerous_function():
    import subprocess
    result = subprocess.run("rm -rf /", shell=True)
    eval("print('dangerous')")
    return result
""",
            language="python"
        )

        result = await validation_pipeline._validate_security(dangerous_code, "/tmp", 90)

        assert result["passed"] is False
        assert "details" in result
        assert result["details"]["total_issues"] > 0
        assert len(result["details"]["issues"]) > 0

        # Check for specific dangerous patterns
        issues = result["details"]["issues"]
        dangerous_patterns_found = [issue for issue in issues if issue["severity"] == "HIGH"]
        assert len(dangerous_patterns_found) > 0

    @pytest.mark.asyncio
    async def test_validate_performance_good_code(self, validation_pipeline, sample_adapter_code):
        """Test performance validation with good code"""
        result = await validation_pipeline._validate_performance(sample_adapter_code, "/tmp", 120)

        assert "score" in result
        assert 0.0 <= result["score"] <= 1.0
        assert "details" in result
        assert "metrics" in result["details"]

    @pytest.mark.asyncio
    async def test_validate_performance_poor_code(self, validation_pipeline):
        """Test performance validation with poor performing code"""
        poor_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="""
import time

async def slow_function():
    for i in range(100):
        for j in range(100):
            for k in range(100):
                time.sleep(0.001)  # Synchronous sleep in async function
    return "done"
""",
            language="python"
        )

        result = await validation_pipeline._validate_performance(poor_code, "/tmp", 120)

        assert "score" in result
        assert result["score"] < 0.8  # Should have lower score
        assert len(result["details"]["issues"]) > 0

    @pytest.mark.asyncio
    async def test_validate_best_practices_good_code(self, validation_pipeline, sample_adapter_code):
        """Test best practices validation with good code"""
        result = await validation_pipeline._validate_best_practices(sample_adapter_code, "/tmp", 60)

        assert "score" in result
        assert result["score"] > 0.5  # Should have decent score due to docstrings and type hints
        assert "details" in result

    @pytest.mark.asyncio
    async def test_validate_best_practices_poor_code(self, validation_pipeline):
        """Test best practices validation with poor code"""
        poor_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="""
def BadFunctionName():
    x = 12345
    y = 67890
    z = x + y + 99999
    return z
""",
            language="python"
        )

        result = await validation_pipeline._validate_best_practices(poor_code, "/tmp", 60)

        assert "score" in result
        assert result["score"] < 0.7  # Should have lower score
        assert len(result["details"]["issues"]) > 0

    @pytest.mark.asyncio
    async def test_validate_production_readiness_good_code(self, validation_pipeline):
        """Test production readiness validation with production-ready code"""
        production_code = AdapterCode(
            source_component="test",
            target_component="test",
            pattern_type="test",
            adapter_code="""
import logging
from config import settings

logger = logging.getLogger(__name__)

class ProductionAdapter:
    def __init__(self, config=None):
        self.config = config or settings
        logger.info("Adapter initialized")

    def process(self, data):
        try:
            logger.debug(f"Processing data: {data}")
            result = self._process_internal(data)
            logger.info("Processing completed successfully")
            return result
        except Exception as e:
            logger.error(f"Processing failed: {e}")
            raise ProcessingError(f"Failed to process data: {e}")
""",
            language="python"
        )

        result = await validation_pipeline._validate_production_readiness(production_code, "/tmp", 90)

        assert "score" in result
        assert result["score"] > 0.7  # Should have good score
        assert result["passed"] is True

    def test_calculate_quality_score(self, validation_pipeline):
        """Test quality score calculation"""
        gate_results = {
            "syntax": {"passed": True, "score": 1.0},
            "compilation": {"passed": True, "score": 1.0},
            "tests": {"passed": True, "score": 0.9},
            "security": {"passed": True, "score": 0.8},
            "performance": {"passed": True, "score": 0.7},
            "best_practices": {"passed": True, "score": 0.6}
        }

        score = validation_pipeline._calculate_quality_score(gate_results)

        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high with all gates passing

    def test_calculate_quality_score_with_failures(self, validation_pipeline):
        """Test quality score calculation with some failures"""
        gate_results = {
            "syntax": {"passed": False},
            "compilation": {"passed": False},
            "tests": {"passed": True, "score": 0.9},
            "security": {"passed": True, "score": 0.8}
        }

        score = validation_pipeline._calculate_quality_score(gate_results)

        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low with syntax and compilation failures

    def test_generate_recommendations(self, validation_pipeline):
        """Test recommendation generation"""
        gate_results = {
            "syntax": {"passed": False, "error": "Missing semicolon"},
            "tests": {"passed": False, "error": "No test coverage"},
            "security": {"passed": True},
            "performance": {
                "passed": False,
                "details": {
                    "recommendations": ["Optimize nested loops", "Use async operations"]
                }
            }
        }

        recommendations = validation_pipeline._generate_recommendations(gate_results)

        assert len(recommendations) > 0
        assert any("syntax" in rec.lower() for rec in recommendations)
        assert any("test" in rec.lower() for rec in recommendations)
        assert "Optimize nested loops" in recommendations
        assert "Use async operations" in recommendations

    def test_track_validation_stats(self, validation_pipeline):
        """Test validation statistics tracking"""
        initial_total = validation_pipeline.validation_stats["total_validations"]

        gates = [ValidationGate.SYNTAX, ValidationGate.TESTS]
        passed = True
        validation_time = 5.0
        gate_results = {
            "syntax": {"passed": True},
            "tests": {"passed": True}
        }

        validation_pipeline._track_validation_stats(gates, passed, validation_time, gate_results)

        stats = validation_pipeline.validation_stats
        assert stats["total_validations"] == initial_total + 1
        assert stats["successful_validations"] == initial_total + 1
        assert stats["average_validation_time"] > 0

    def test_track_validation_stats_with_failures(self, validation_pipeline):
        """Test validation statistics tracking with failures"""
        initial_total = validation_pipeline.validation_stats["total_validations"]

        gates = [ValidationGate.SYNTAX, ValidationGate.SECURITY]
        passed = False
        validation_time = 3.0
        gate_results = {
            "syntax": {"passed": False},
            "security": {"passed": True}
        }

        validation_pipeline._track_validation_stats(gates, passed, validation_time, gate_results)

        stats = validation_pipeline.validation_stats
        assert stats["total_validations"] == initial_total + 1
        assert stats["gate_failure_counts"]["syntax"] >= 1

    def test_get_validation_statistics(self, validation_pipeline):
        """Test validation statistics retrieval"""
        # Add some mock stats
        validation_pipeline.validation_stats["total_validations"] = 10
        validation_pipeline.validation_stats["successful_validations"] = 8

        stats = validation_pipeline.get_validation_statistics()

        assert stats["success_rate"] == 0.8
        assert "available_gates" in stats
        assert "enabled_gates" in stats
        assert "quality_thresholds" in stats
        assert len(stats["available_gates"]) > 0

    def test_get_validation_recommendations_python(self, validation_pipeline):
        """Test validation recommendations for Python"""
        recommendations = validation_pipeline.get_validation_recommendations("python", {})

        assert len(recommendations) > 0

        # Check for Python-specific recommendations
        categories = [rec["category"] for rec in recommendations]
        assert "syntax" in categories
        assert "security" in categories
        assert "testing" in categories

        # Check for specific tools
        tools_mentioned = []
        for rec in recommendations:
            tools_mentioned.extend(rec.get("tools", []))
        assert "bandit" in tools_mentioned
        assert "pytest" in tools_mentioned

    def test_get_validation_recommendations_javascript(self, validation_pipeline):
        """Test validation recommendations for JavaScript"""
        recommendations = validation_pipeline.get_validation_recommendations("javascript", {})

        assert len(recommendations) > 0

        # Check for JavaScript-specific tools
        tools_mentioned = []
        for rec in recommendations:
            tools_mentioned.extend(rec.get("tools", []))
        assert "eslint" in tools_mentioned
        assert "jest" in tools_mentioned

    def test_get_validation_recommendations_with_context(self, validation_pipeline):
        """Test validation recommendations with project context"""
        project_context = {
            "has_database": True,
            "has_api": True
        }

        recommendations = validation_pipeline.get_validation_recommendations("python", project_context)

        assert len(recommendations) > 0

        # Should include database and API specific recommendations
        rec_text = " ".join([rec["recommendation"] for rec in recommendations])
        assert "database" in rec_text.lower() or "sql" in rec_text.lower()
        assert "api" in rec_text.lower()

    def test_get_file_extension(self, validation_pipeline):
        """Test file extension detection"""
        assert validation_pipeline._get_file_extension("python") == "py"
        assert validation_pipeline._get_file_extension("javascript") == "js"
        assert validation_pipeline._get_file_extension("typescript") == "ts"
        assert validation_pipeline._get_file_extension("java") == "java"
        assert validation_pipeline._get_file_extension("unknown") == "txt"

    def test_create_failed_validation_result(self, validation_pipeline, sample_adapter_code):
        """Test failed validation result creation"""
        error_message = "Validation failed due to critical errors"

        result = validation_pipeline._create_failed_validation_result(sample_adapter_code, error_message)

        assert isinstance(result, ValidationResult)
        assert result.status == ValidationStatus.FAILED
        assert result.passed is False
        assert error_message in result.syntax_errors
        assert result.performance_score == 0.0
        assert len(result.improvement_suggestions) > 0
